import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { PlusIcon, DocumentArrowUpIcon } from '@heroicons/react/24/outline';
import { invoicesApi } from '../services/api';
import { useTenant } from '../contexts/TenantContext';
import { Invoice } from '../types';
import toast from 'react-hot-toast';
import '../appCustomStyles.css';

export default function InvoicesPage() {
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const { currentTenant, hasPermission } = useTenant();
  const queryClient = useQueryClient();

  const { data: invoices = [], isLoading } = useQuery(
    ['invoices', currentTenant?.id, selectedStatus],
    () => invoicesApi.getInvoices({ status: selectedStatus || undefined }),
    { enabled: !!currentTenant }
  );

  const uploadMutation = useMutation(
    ({ file, supplierName }: { file: File; supplierName?: string }) =>
      invoicesApi.uploadInvoice(file, supplierName),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['invoices']);
        setIsUploadModalOpen(false);
        toast.success('Invoice uploaded successfully!');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Upload failed');
      },
    }
  );

  const validateMutation = useMutation(
    (invoiceId: string) => invoicesApi.validateInvoice(invoiceId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['invoices']);
        toast.success('Invoice validated successfully!');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Validation failed');
      },
    }
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      case 'needs_review':
        return 'bg-orange-100 text-orange-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatAmount = (amount?: number, currency?: string) => {
    if (!amount) return 'N/A';
    return `${amount.toLocaleString()} ${currency || ''}`;
  };

  if (!currentTenant) {
    return (
      <div className="page-background">
        <div className="content-container">
          <div className="card-container text-center">
            <p className="text-gray-500">Please select a tenant to view invoices.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="page-background">
      <div className="content-container">
        <div className="card-container-large mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="page-title custom-gradient-text">Invoices</h1>
              <p className="text-gray-700">
                Manage and process invoices for {currentTenant.name}
              </p>
            </div>
            <div className="mt-4 sm:mt-0">
              {hasPermission('invoices:write') && (
                <button
                  type="button"
                  onClick={() => setIsUploadModalOpen(true)}
                  className="btn-small"
                >
                  <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                  Upload Invoice
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="filter-container">
          <h3 className="section-title">Filters</h3>
          <div className="filter-grid">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="select-custom"
            >
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="completed">Completed</option>
              <option value="needs_review">Needs Review</option>
              <option value="failed">Failed</option>
            </select>
          </div>
        </div>

        {/* Invoices Table */}
        <div className="table-container">
          <h3 className="section-title">Invoices</h3>
          <div className="overflow-x-auto">
            <table className="table-custom">
              <thead className="table-header">
                <tr>
                  <th className="table-header-cell">
                    Supplier
                  </th>
                  <th className="table-header-cell">
                    Invoice #
                  </th>
                  <th className="table-header-cell">
                    Amount
                  </th>
                  <th className="table-header-cell">
                    Status
                  </th>
                  <th className="table-header-cell">
                    Created
                  </th>
                  <th className="table-header-cell">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {isLoading ? (
                  <tr>
                    <td colSpan={6} className="table-cell text-center">
                      <div className="loading-container">
                        <div className="loading-spinner"></div>
                      </div>
                    </td>
                  </tr>
                ) : invoices.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="table-cell text-center text-gray-500">
                      No invoices found
                    </td>
                  </tr>
                ) : (
                  invoices.map((invoice: Invoice) => (
                    <tr key={invoice.id} className="table-row">
                      <td className="table-cell font-medium">
                        {invoice.supplier_name}
                      </td>
                      <td className="table-cell text-gray-500">
                        {invoice.invoice_number || 'N/A'}
                      </td>
                      <td className="table-cell text-gray-500">
                        {formatAmount(invoice.total_amount, invoice.currency)}
                      </td>
                      <td className="table-cell">
                        <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${getStatusColor(invoice.status)}`}>
                          {invoice.status}
                        </span>
                      </td>
                      <td className="table-cell text-gray-500">
                        {formatDate(invoice.created_at)}
                      </td>
                      <td className="table-cell text-right">
                        {invoice.status === 'needs_review' && hasPermission('accounting:validate') && (
                          <button
                            onClick={() => validateMutation.mutate(invoice.id)}
                            disabled={validateMutation.isLoading}
                            className="text-indigo-600 hover:text-indigo-900 mr-4 font-medium"
                          >
                            Validate
                          </button>
                        )}
                        <button className="text-indigo-600 hover:text-indigo-900 font-medium">
                          View
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Upload Modal */}
        {isUploadModalOpen && (
          <UploadModal
            onClose={() => setIsUploadModalOpen(false)}
            onUpload={(file, supplierName) => uploadMutation.mutate({ file, supplierName })}
            isLoading={uploadMutation.isLoading}
          />
        )}
      </div>
    </div>
  );
}

interface UploadModalProps {
  onClose: () => void;
  onUpload: (file: File, supplierName?: string) => void;
  isLoading: boolean;
}

function UploadModal({ onClose, onUpload, isLoading }: UploadModalProps) {
  const [file, setFile] = useState<File | null>(null);
  const [supplierName, setSupplierName] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (file) {
      onUpload(file, supplierName || undefined);
    }
  };

  return (
    <div className="fixed inset-0 z-10 overflow-y-auto">
      <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

        <div className="inline-block transform overflow-hidden rounded-3xl bg-white bg-opacity-95 px-6 pt-6 pb-6 text-left align-bottom shadow-2xl border border-purple-200 transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-8 sm:align-middle">
          <form onSubmit={handleSubmit}>
            <div>
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-indigo-100 to-purple-100">
                <DocumentArrowUpIcon className="h-8 w-8 text-indigo-600" aria-hidden="true" />
              </div>
              <div className="mt-4 text-center sm:mt-6">
                <h3 className="text-xl font-semibold leading-6 text-gray-900 custom-gradient-text">
                  Upload Invoice
                </h3>
                <div className="mt-2">
                  <p className="text-sm text-gray-600">
                    Upload a PDF, PNG, or JPG file of the invoice.
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-6 space-y-6 bg-gradient-to-r from-purple-50 via-indigo-50 to-pink-50 rounded-2xl p-6">
              <div>
                <label htmlFor="supplier" className="block text-sm font-medium text-gray-700 mb-2">
                  Supplier Name (Optional)
                </label>
                <input
                  type="text"
                  id="supplier"
                  value={supplierName}
                  onChange={(e) => setSupplierName(e.target.value)}
                  className="input-custom"
                  placeholder="Enter supplier name"
                />
              </div>

              <div>
                <label htmlFor="file" className="block text-sm font-medium text-gray-700 mb-2">
                  Invoice File
                </label>
                <input
                  type="file"
                  id="file"
                  accept=".pdf,.png,.jpg,.jpeg"
                  onChange={(e) => setFile(e.target.files?.[0] || null)}
                  className="input-custom file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                  required
                />
              </div>
            </div>

            <div className="mt-6 sm:mt-8 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-4">
              <button
                type="submit"
                disabled={!file || isLoading}
                className="btn-primary sm:col-start-2"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                    Uploading...
                  </div>
                ) : (
                  'Upload'
                )}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary mt-3 sm:col-start-1 sm:mt-0"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
