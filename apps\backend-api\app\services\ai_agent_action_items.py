from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from datetime import datetime
import logging

from app.models.action_item import ActionItem
from app.models.agent_session import AgentSession, SessionStatus
from app.models.user import User, TenantUser
from app.database import set_tenant_context

logger = logging.getLogger(__name__)


class AIAgentActionItemService:
    """Service for managing action items related to AI agent sessions"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_action_item_for_failed_session(
        self, 
        session: AgentSession,
        assigned_user_id: Optional[str] = None
    ) -> ActionItem:
        """
        Create an action item for a failed AI agent session
        
        Args:
            session: The failed AgentSession
            assigned_user_id: Optional specific user to assign to, otherwise assigns to tenant admin
            
        Returns:
            Created ActionItem
        """
        set_tenant_context(self.db, str(session.tenant_id))
        
        # Determine who to assign the action item to
        if not assigned_user_id:
            assigned_user_id = self._get_default_assignee(session.tenant_id)
        
        # Determine priority based on failure reason
        priority = self._determine_priority(session)
        
        # Create descriptive title and description
        title = self._generate_title(session)
        description = self._generate_description(session)
        
        # Determine category
        category = self._determine_category(session)
        
        action_item = ActionItem(
            tenant_id=session.tenant_id,
            user_id=assigned_user_id,
            agent_session_id=session.id,
            title=title,
            description=description,
            priority=priority,
            category=category
        )
        
        self.db.add(action_item)
        self.db.commit()
        self.db.refresh(action_item)
        
        logger.info(f"Created action item {action_item.id} for failed session {session.id}")
        return action_item
    
    def _get_default_assignee(self, tenant_id: str) -> str:
        """Get default user to assign action items to (tenant admin)"""
        # Find a tenant admin or the first active user
        tenant_user = self.db.query(TenantUser).join(User).filter(
            TenantUser.tenant_id == tenant_id,
            User.is_active == True,
            TenantUser.role.has(name="admin")  # Prefer admin users
        ).first()
        
        if not tenant_user:
            # Fallback to any active user in the tenant
            tenant_user = self.db.query(TenantUser).join(User).filter(
                TenantUser.tenant_id == tenant_id,
                User.is_active == True
            ).first()
        
        if not tenant_user:
            raise ValueError(f"No active users found for tenant {tenant_id}")
        
        return str(tenant_user.user_id)
    
    def _determine_priority(self, session: AgentSession) -> str:
        """Determine priority based on session failure reason"""
        reason = session.action_item_reason or ""
        reason_lower = reason.lower()
        
        # High priority for system errors
        if any(keyword in reason_lower for keyword in ["error", "exception", "failed", "timeout"]):
            return "high"
        
        # Medium priority for duplicates
        if "duplicate" in reason_lower:
            return "medium"
        
        # Low priority for confidence issues
        if "confidence" in reason_lower:
            return "low"
        
        # Default to medium
        return "medium"
    
    def _determine_category(self, session: AgentSession) -> str:
        """Determine category based on session failure reason"""
        reason = session.action_item_reason or ""
        reason_lower = reason.lower()
        
        if "duplicate" in reason_lower:
            return "validation"
        elif any(keyword in reason_lower for keyword in ["error", "exception", "failed"]):
            return "error"
        elif "confidence" in reason_lower:
            return "review"
        else:
            return "manual_entry"
    
    def _generate_title(self, session: AgentSession) -> str:
        """Generate a descriptive title for the action item"""
        base_title = f"AI Agent Session Failed: {session.session_name}"
        
        reason = session.action_item_reason or ""
        if "duplicate" in reason.lower():
            return f"Duplicate Invoice Detected - {session.session_name}"
        elif "confidence" in reason.lower():
            return f"Low Confidence Processing - {session.session_name}"
        elif "error" in reason.lower():
            return f"Processing Error - {session.session_name}"
        else:
            return base_title
    
    def _generate_description(self, session: AgentSession) -> str:
        """Generate a detailed description for the action item"""
        description_parts = [
            f"AI Agent session {session.session_name} failed and requires human review.",
            f"",
            f"Session Details:",
            f"- Session ID: {session.id}",
            f"- Execution Plan: {session.execution_plan.name if session.execution_plan else 'Unknown'}",
            f"- Source Type: {session.source_type}",
            f"- Invoice ID: {session.invoice_unique_id or 'N/A'}",
            f"- Created: {session.created_at.strftime('%Y-%m-%d %H:%M:%S')}",
            f"- Failed: {session.completed_at.strftime('%Y-%m-%d %H:%M:%S') if session.completed_at else 'N/A'}",
            f"- Retry Count: {session.retry_count}/{session.max_retries}",
            f"",
            f"Failure Reason:",
            f"{session.action_item_reason or 'No specific reason provided'}",
            f"",
            f"Next Steps:",
        ]
        
        # Add specific next steps based on failure type
        reason = session.action_item_reason or ""
        if "duplicate" in reason.lower():
            description_parts.extend([
                "1. Review the duplicate detection results",
                "2. Verify if this is truly a duplicate invoice",
                "3. If not a duplicate, update the invoice metadata and retry",
                "4. If it is a duplicate, mark this session as resolved"
            ])
        elif "confidence" in reason.lower():
            description_parts.extend([
                "1. Review the AI agent's accounting suggestions",
                "2. Verify the extracted invoice data",
                "3. Manually adjust accounting entries if needed",
                "4. Complete the invoice processing manually or retry with corrections"
            ])
        else:
            description_parts.extend([
                "1. Review the session logs and error details",
                "2. Check the thought chain for debugging information",
                "3. Fix any underlying issues",
                "4. Retry the session or process manually"
            ])
        
        return "\n".join(description_parts)
    
    def resolve_action_item(
        self, 
        action_item_id: str, 
        user_id: str, 
        resolution_notes: str,
        retry_session: bool = False
    ) -> ActionItem:
        """
        Resolve an action item and optionally retry the associated session
        
        Args:
            action_item_id: ID of the action item to resolve
            user_id: ID of the user resolving the item
            resolution_notes: Notes about the resolution
            retry_session: Whether to retry the associated AI agent session
            
        Returns:
            Updated ActionItem
        """
        action_item = self.db.query(ActionItem).filter(ActionItem.id == action_item_id).first()
        if not action_item:
            raise ValueError(f"Action item {action_item_id} not found")
        
        set_tenant_context(self.db, str(action_item.tenant_id))
        
        # Update action item
        action_item.is_completed = True
        action_item.completed_by = user_id
        action_item.resolution_notes = resolution_notes
        action_item.updated_at = datetime.utcnow()
        
        # If retry requested and there's an associated session
        if retry_session and action_item.agent_session_id:
            from app.tasks.ai_agent_tasks import retry_failed_session_task
            
            # Start retry task
            retry_failed_session_task.delay(str(action_item.agent_session_id))
            
            # Add note about retry
            action_item.resolution_notes += f"\n\nSession retry initiated at {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}"
        
        self.db.commit()
        self.db.refresh(action_item)
        
        logger.info(f"Resolved action item {action_item_id} by user {user_id}")
        return action_item
    
    def get_action_items_for_session(self, session_id: str) -> List[ActionItem]:
        """Get all action items associated with a session"""
        return self.db.query(ActionItem).filter(
            ActionItem.agent_session_id == session_id
        ).order_by(ActionItem.created_at.desc()).all()
    
    def get_pending_action_items(self, tenant_id: str, limit: int = 50) -> List[ActionItem]:
        """Get pending action items for a tenant"""
        set_tenant_context(self.db, tenant_id)
        
        return self.db.query(ActionItem).filter(
            ActionItem.tenant_id == tenant_id,
            ActionItem.is_completed == False
        ).order_by(
            ActionItem.priority.desc(),  # High priority first
            ActionItem.created_at.asc()   # Oldest first within same priority
        ).limit(limit).all()
    
    def get_action_item_statistics(self, tenant_id: str, days: int = 30) -> Dict[str, Any]:
        """Get statistics about action items for a tenant"""
        from datetime import timedelta
        
        set_tenant_context(self.db, tenant_id)
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Get all action items in the period
        action_items = self.db.query(ActionItem).filter(
            ActionItem.tenant_id == tenant_id,
            ActionItem.created_at >= cutoff_date
        ).all()
        
        # Calculate statistics
        total_items = len(action_items)
        completed_items = len([item for item in action_items if item.is_completed])
        pending_items = total_items - completed_items
        
        # Group by category
        category_stats = {}
        for item in action_items:
            category = item.category
            if category not in category_stats:
                category_stats[category] = {"total": 0, "completed": 0, "pending": 0}
            
            category_stats[category]["total"] += 1
            if item.is_completed:
                category_stats[category]["completed"] += 1
            else:
                category_stats[category]["pending"] += 1
        
        # Group by priority
        priority_stats = {}
        for item in action_items:
            priority = item.priority
            if priority not in priority_stats:
                priority_stats[priority] = {"total": 0, "completed": 0, "pending": 0}
            
            priority_stats[priority]["total"] += 1
            if item.is_completed:
                priority_stats[priority]["completed"] += 1
            else:
                priority_stats[priority]["pending"] += 1
        
        return {
            "period_days": days,
            "total_action_items": total_items,
            "completed_action_items": completed_items,
            "pending_action_items": pending_items,
            "completion_rate": completed_items / total_items if total_items > 0 else 0,
            "category_statistics": category_stats,
            "priority_statistics": priority_stats
        }
