from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
import os
import aiofiles

from app.database import get_db
from app.models.invoice import Invoice, AccountingEntry
from app.models.user import TenantUser
from app.middleware import get_current_tenant_user
from app.utils.permissions import Permission, check_permission
from app.utils.security import generate_secure_filename
from app.config import settings
from app.tasks.ai_agent_tasks import ai_agent_process_invoice_task
from app.services.duplicate_detection import DuplicateDetectionService

router = APIRouter()


@router.post("/upload")
async def upload_invoice(
    file: UploadFile = File(...),
    supplier_name: Optional[str] = Form(None),
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """Upload and process an invoice file"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    # Validate file type
    allowed_types = ["application/pdf", "image/png", "image/jpeg", "image/jpg"]
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File type not supported. Please upload PDF, PNG, or JPG files."
        )
    
    # Validate file size
    if file.size > settings.max_file_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File too large. Maximum size is {settings.max_file_size / (1024*1024):.1f}MB"
        )
    
    # Generate secure filename
    secure_filename = generate_secure_filename(file.filename)
    file_path = os.path.join(settings.upload_dir, str(tenant_user.tenant_id), secure_filename)
    
    # Ensure upload directory exists
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # Save file
    try:
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save file"
        )

    # Create content hash for duplicate detection
    duplicate_service = DuplicateDetectionService(db)
    content_hash = duplicate_service.create_content_hash(file_path)
    
    # Create invoice record
    invoice = Invoice(
        tenant_id=tenant_user.tenant_id,
        supplier_name=supplier_name or "Unknown",
        original_filename=file.filename,
        file_path=file_path,
        file_type=file.content_type.split('/')[-1],
        status="pending"
    )
    
    db.add(invoice)
    db.commit()
    db.refresh(invoice)
    
    # Prepare source metadata for AI agent
    source_metadata = {
        "file_path": file_path,
        "file_type": file.content_type.split('/')[-1],
        "filename": file.filename,
        "file_size": file.size,
        "content_hash": content_hash,
        "supplier_name": supplier_name,
        "upload_user_id": str(tenant_user.user_id),
        "invoice_db_id": str(invoice.id)  # Link to invoice record
    }

    # Start AI agent processing
    ai_agent_process_invoice_task.delay(
        tenant_id=str(tenant_user.tenant_id),
        execution_plan_name="invoice_processing_v1",
        invoice_unique_id=None,  # No unique ID for manual uploads
        source_type="manual",
        source_metadata=source_metadata
    )

    return {
        "id": invoice.id,
        "message": "Invoice uploaded successfully and AI agent processing started",
        "status": invoice.status,
        "processing_type": "ai_agent"
    }


@router.get("/", response_model=List[dict])
async def list_invoices(
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """List invoices for current tenant"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    query = db.query(Invoice).filter(Invoice.tenant_id == tenant_user.tenant_id)
    
    if status_filter:
        query = query.filter(Invoice.status == status_filter)
    
    invoices = query.offset(skip).limit(limit).all()
    
    return [
        {
            "id": invoice.id,
            "supplier_name": invoice.supplier_name,
            "invoice_number": invoice.invoice_number,
            "total_amount": invoice.total_amount,
            "currency": invoice.currency,
            "status": invoice.status,
            "created_at": invoice.created_at,
            "original_filename": invoice.original_filename
        }
        for invoice in invoices
    ]


@router.get("/{invoice_id}")
async def get_invoice(
    invoice_id: UUID,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """Get invoice details"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    invoice = db.query(Invoice).filter(
        Invoice.id == invoice_id,
        Invoice.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    # Get accounting entries
    accounting_entries = db.query(AccountingEntry).filter(
        AccountingEntry.invoice_id == invoice_id,
        AccountingEntry.tenant_id == tenant_user.tenant_id
    ).all()
    
    return {
        "id": invoice.id,
        "supplier_name": invoice.supplier_name,
        "invoice_number": invoice.invoice_number,
        "invoice_date": invoice.invoice_date,
        "due_date": invoice.due_date,
        "total_amount": invoice.total_amount,
        "currency": invoice.currency,
        "status": invoice.status,
        "extracted_text": invoice.extracted_text,
        "extracted_context": invoice.extracted_context,
        "processing_error": invoice.processing_error,
        "created_at": invoice.created_at,
        "updated_at": invoice.updated_at,
        "original_filename": invoice.original_filename,
        "accounting_entries": [
            {
                "id": entry.id,
                "account_code": entry.account_code,
                "account_name": entry.account_name,
                "debit_amount": entry.debit_amount,
                "credit_amount": entry.credit_amount,
                "description": entry.description,
                "confidence_score": entry.confidence_score,
                "is_validated": entry.is_validated
            }
            for entry in accounting_entries
        ]
    }


@router.put("/{invoice_id}/validate")
async def validate_invoice(
    invoice_id: UUID,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """Validate invoice accounting entries"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACCOUNTING_VALIDATE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    invoice = db.query(Invoice).filter(
        Invoice.id == invoice_id,
        Invoice.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    # Update accounting entries as validated
    accounting_entries = db.query(AccountingEntry).filter(
        AccountingEntry.invoice_id == invoice_id,
        AccountingEntry.tenant_id == tenant_user.tenant_id
    ).all()
    
    for entry in accounting_entries:
        entry.is_validated = True
        entry.validated_by = tenant_user.user_id
    
    # Update invoice status
    invoice.status = "completed"
    
    db.commit()
    
    return {"message": "Invoice validated successfully"}


@router.delete("/{invoice_id}")
async def delete_invoice(
    invoice_id: UUID,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """Delete an invoice"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_DELETE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    invoice = db.query(Invoice).filter(
        Invoice.id == invoice_id,
        Invoice.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    # Delete file
    try:
        if os.path.exists(invoice.file_path):
            os.remove(invoice.file_path)
    except Exception:
        pass  # Continue even if file deletion fails
    
    # Delete from database
    db.delete(invoice)
    db.commit()
    
    return {"message": "Invoice deleted successfully"}
