#!/usr/bin/env python3
"""
Test script for the new integration endpoints.
This script tests the new API endpoints we added for integration management.
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_endpoint(method, endpoint, data=None, headers=None):
    """Test an API endpoint and return the response"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, headers=headers)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers)
        else:
            print(f"❌ Unsupported method: {method}")
            return None
            
        print(f"🔍 {method.upper()} {endpoint}")
        print(f"   Status: {response.status_code}")
        
        if response.status_code < 400:
            print(f"   ✅ Success")
            if response.content:
                try:
                    data = response.json()
                    print(f"   Response: {json.dumps(data, indent=2)[:200]}...")
                except:
                    print(f"   Response: {response.text[:200]}...")
        else:
            print(f"   ❌ Error: {response.text}")
            
        return response
        
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection error - make sure the server is running on {BASE_URL}")
        return None
    except Exception as e:
        print(f"❌ Error testing {endpoint}: {e}")
        return None

def main():
    print("🧪 Testing Integration Management Endpoints")
    print("=" * 50)
    
    # Test available integrations endpoint
    print("\n1. Testing available integrations...")
    test_endpoint("GET", "/api/v1/integrations/available")
    
    # Test integrations list endpoint (will fail without auth, but should return 401)
    print("\n2. Testing integrations list...")
    test_endpoint("GET", "/api/v1/integrations")
    
    # Test schedule settings endpoint (will fail without auth, but should return 401)
    print("\n3. Testing schedule settings...")
    test_endpoint("GET", "/api/v1/integrations/schedule")
    
    # Test sync endpoints (will fail without auth, but should return 401)
    print("\n4. Testing sync all endpoint...")
    test_endpoint("POST", "/api/v1/integrations/sync-all")
    
    print("\n5. Testing trigger scheduled sync...")
    test_endpoint("POST", "/api/v1/integrations/trigger-scheduled-sync")
    
    # Test OpenAPI docs
    print("\n6. Testing OpenAPI documentation...")
    test_endpoint("GET", "/docs")
    
    print("\n" + "=" * 50)
    print("✅ Endpoint testing completed!")
    print("\nNote: Most endpoints return 401 (Unauthorized) which is expected")
    print("since we're not providing authentication tokens. This confirms")
    print("the endpoints exist and are properly protected.")
    print("\nTo test with authentication:")
    print("1. Login through the frontend at http://localhost:3000")
    print("2. Navigate to Settings > Integrations")
    print("3. Test the UI functionality")

if __name__ == "__main__":
    main()
