from celery import current_task
from sqlalchemy.orm import Session
import logging
import asyncio
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any

from app.celery_app import celery_app
from app.database import SessionLocal, set_tenant_context
from app.models.invoice import Invoice
from app.models.tenant import Tenant
from app.services.integration_service import IntegrationService
from app.tasks.ai_agent_tasks import ai_agent_process_invoice_task

logger = logging.getLogger(__name__)


async def download_invoice_file(file_url: str, local_path: str, headers: Dict[str, str] = None) -> bool:
    """
    Download invoice file from URL to local path.

    Args:
        file_url: URL to download from
        local_path: Local file path to save to
        headers: Optional HTTP headers

    Returns:
        True if download was successful
    """
    import httpx

    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(local_path), exist_ok=True)

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(file_url, headers=headers or {})
            response.raise_for_status()

            with open(local_path, 'wb') as f:
                f.write(response.content)

            logger.info(f"Downloaded invoice file to {local_path}")
            return True

    except Exception as e:
        logger.error(f"Error downloading invoice file from {file_url}: {e}")
        return False


@celery_app.task(bind=True)
def fetch_invoices_from_erp_task(self):
    """Scheduled task to fetch new invoices from integration systems"""
    db = SessionLocal()
    try:
        # Get all active tenants
        tenants = db.query(Tenant).filter(Tenant.is_active == True).all()

        total_processed = 0
        integration_service = IntegrationService(db)

        for tenant in tenants:
            try:
                # Set tenant context
                set_tenant_context(db, str(tenant.id))

                # Get last fetch time (or default to 24 hours ago)
                last_fetch = datetime.now(datetime.timezone.utc) - timedelta(hours=24)

                logger.info(f"Fetching invoices for tenant {tenant.id} since {last_fetch}")

                # Fetch invoices from all active integrations for this tenant
                sync_result = asyncio.run(integration_service.sync_all_tenant_integrations(
                    str(tenant.id),
                    last_fetch
                ))

                invoices_data = sync_result.get("invoices", [])

                for invoice_data in invoices_data:
                    try:
                        # Check if invoice already exists (by external_id or invoice_number)
                        existing = db.query(Invoice).filter(
                            Invoice.tenant_id == tenant.id,
                            Invoice.invoice_number == invoice_data.get("invoice_number")
                        ).first()

                        if existing:
                            logger.info(f"Invoice {invoice_data['invoice_number']} already exists for tenant {tenant.id}")
                            continue

                        # Download invoice file if URL is provided
                        file_path = None
                        original_filename = f"{invoice_data['invoice_number']}.{invoice_data['file_type']}"

                        if invoice_data.get("file_url"):
                            from app.config import settings
                            from app.utils.security import generate_secure_filename

                            secure_filename = generate_secure_filename(original_filename)
                            file_path = os.path.join(
                                settings.upload_dir,
                                str(tenant.id),
                                "integrations",
                                secure_filename
                            )

                            if not asyncio.run(download_invoice_file(invoice_data["file_url"], file_path)):
                                logger.error(f"Failed to download invoice file for {invoice_data['invoice_number']}")
                                continue

                        # Create invoice record
                        invoice = Invoice(
                            tenant_id=tenant.id,
                            supplier_name=invoice_data["supplier_name"],
                            invoice_number=invoice_data["invoice_number"],
                            invoice_date=invoice_data.get("invoice_date"),
                            due_date=invoice_data.get("due_date"),
                            total_amount=invoice_data.get("total_amount"),
                            currency=invoice_data.get("currency", "SEK"),
                            original_filename=original_filename,
                            file_path=file_path or "",
                            file_type=invoice_data["file_type"],
                            status="pending",
                            raw_data=invoice_data
                        )

                        db.add(invoice)
                        db.commit()
                        db.refresh(invoice)

                        # Start AI agent processing
                        ai_agent_process_invoice_task.delay(
                            tenant_id=str(tenant.id),
                            execution_plan_name="invoice_processing_v1",
                            invoice_unique_id=invoice_data.get("id") or invoice_data.get("invoice_id"),
                            source_type="erp",
                            source_metadata={
                                "integration_type": integration_type,
                                "original_data": invoice_data,
                                "invoice_db_id": str(invoice.id)
                            }
                        )

                        total_processed += 1
                        logger.info(f"Created and queued invoice {invoice.id} from integration")

                    except Exception as e:
                        logger.error(f"Error processing integration invoice {invoice_data.get('invoice_number', 'unknown')}: {e}")
                        db.rollback()
                        continue

            except Exception as e:
                logger.error(f"Error fetching invoices for tenant {tenant.id}: {e}")
                continue

        logger.info(f"Integration fetch task completed. Processed {total_processed} new invoices")
        return {"processed_invoices": total_processed}
        
    except Exception as e:
        logger.error(f"Error in ERP fetch task: {e}")
        raise
    finally:
        db.close()


@celery_app.task
def sync_invoice_status_to_integration_task(invoice_id: str):
    """Sync invoice processing status back to integration system"""
    db = SessionLocal()
    try:
        invoice = db.query(Invoice).filter(Invoice.id == invoice_id).first()
        if not invoice:
            logger.error(f"Invoice {invoice_id} not found")
            return {"error": "Invoice not found"}

        # Set tenant context
        set_tenant_context(db, str(invoice.tenant_id))

        # Get integration ID from invoice metadata
        raw_data = invoice.raw_data or {}
        integration_id = raw_data.get("integration_id")
        external_id = raw_data.get("external_id", invoice.invoice_number)

        if not integration_id:
            logger.warning(f"No integration ID found for invoice {invoice_id}")
            return {"warning": "No integration ID found"}

        # Prepare status update data
        status_data = {
            "invoice_id": invoice.invoice_number,
            "processing_status": invoice.status,
            "processed_at": invoice.updated_at.isoformat() if invoice.updated_at else None,
            "accounting_entries": []
        }

        # Include accounting entries if completed
        if invoice.status == "completed":
            for entry in invoice.accounting_entries:
                if entry.is_validated:
                    status_data["accounting_entries"].append({
                        "account_code": entry.account_code,
                        "account_name": entry.account_name,
                        "debit_amount": entry.debit_amount,
                        "credit_amount": entry.credit_amount,
                        "description": entry.description
                    })

        # Send callback to integration
        integration_service = IntegrationService(db)
        success = asyncio.run(integration_service.send_post_processing_callback(
            integration_id,
            external_id,
            invoice.status,
            status_data
        ))

        if success:
            return {"status": "success", "synced_data": status_data}
        else:
            return {"status": "failed", "message": "Failed to send callback to integration"}

    except Exception as e:
        logger.error(f"Error syncing invoice status to integration: {e}")
        raise
    finally:
        db.close()


@celery_app.task
def validate_integration_connections_task(tenant_id: str):
    """Validate all integration connections for a tenant"""
    db = SessionLocal()
    try:
        integration_service = IntegrationService(db)

        # Get all integrations for tenant
        integrations = integration_service.get_active_integrations(tenant_id)

        if not integrations:
            return {
                "connection_status": "no_integrations",
                "message": "No active integrations found for tenant",
                "tenant_id": tenant_id
            }

        results = []
        all_successful = True

        for integration in integrations:
            try:
                test_result = asyncio.run(integration_service.test_integration(str(integration.id)))
                results.append({
                    "integration_id": str(integration.id),
                    "integration_type": integration.integration_type,
                    "name": integration.name,
                    "test_result": test_result
                })

                if not test_result.get("success", False):
                    all_successful = False

            except Exception as e:
                results.append({
                    "integration_id": str(integration.id),
                    "integration_type": integration.integration_type,
                    "name": integration.name,
                    "test_result": {
                        "success": False,
                        "message": f"Test failed: {str(e)}"
                    }
                })
                all_successful = False

        overall_status = "success" if all_successful else "partial" if any(r["test_result"]["success"] for r in results) else "failed"

        result = {
            "connection_status": overall_status,
            "tenant_id": tenant_id,
            "integrations_tested": len(results),
            "successful_tests": sum(1 for r in results if r["test_result"]["success"]),
            "failed_tests": sum(1 for r in results if not r["test_result"]["success"]),
            "last_validation": datetime.utcnow().isoformat(),
            "results": results
        }

        logger.info(f"Integration connections validated for tenant {tenant_id}: {overall_status}")
        return result

    except Exception as e:
        logger.error(f"Integration connection validation failed for tenant {tenant_id}: {e}")
        return {"connection_status": "failed", "error": str(e), "tenant_id": tenant_id}
    finally:
        db.close()
