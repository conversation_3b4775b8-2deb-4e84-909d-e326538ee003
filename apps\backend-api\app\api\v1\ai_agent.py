from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import uuid
import logging

from app.database import get_db, set_tenant_context
from app.models.agent_session import (
    AgentSession, ExecutionPlan, ExecutionStep, ToolResult, ThoughtChain,
    SessionStatus, StepStatus
)
from app.schemas.ai_agent import (
    ExecutionPlanCreate, ExecutionPlanUpdate, ExecutionPlanResponse,
    AgentSessionResponse, AgentSessionCreate, ExecutionStepResponse,
    ThoughtChainResponse, ToolResultResponse, SessionMetrics,
    SessionDetailResponse, ThoughtChainSummary
)
from app.middleware import get_current_active_user as get_current_user
from app.models.user import User, TenantUser
from app.tasks.ai_agent_tasks import ai_agent_process_invoice_task, retry_failed_session_task

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/execution-plans", response_model=List[ExecutionPlanResponse])
async def list_execution_plans(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """List all execution plans"""
    plans = db.query(ExecutionPlan).filter(ExecutionPlan.is_active == True).all()
    return plans


@router.get("/execution-plans/{plan_id}", response_model=ExecutionPlanResponse)
async def get_execution_plan(
    plan_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific execution plan"""
    plan = db.query(ExecutionPlan).filter(ExecutionPlan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Execution plan not found")
    return plan


@router.post("/execution-plans", response_model=ExecutionPlanResponse)
async def create_execution_plan(
    plan_data: ExecutionPlanCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new execution plan (admin only)"""
    try:
        # Check if name already exists
        existing = db.query(ExecutionPlan).filter(ExecutionPlan.name == plan_data.name).first()
        if existing:
            raise HTTPException(status_code=400, detail="Execution plan name already exists")
        
        # Validate steps format
        if not plan_data.steps or not isinstance(plan_data.steps, list):
            raise HTTPException(status_code=400, detail="Steps must be a non-empty list")
        
        for step in plan_data.steps:
            if not isinstance(step, dict) or "step" not in step:
                raise HTTPException(status_code=400, detail="Each step must have a 'step' field")
        
        plan = ExecutionPlan(
            name=plan_data.name,
            description=plan_data.description,
            version=plan_data.version,
            steps=plan_data.steps,
            global_config=plan_data.global_config or {}
        )
        
        db.add(plan)
        db.commit()
        db.refresh(plan)
        
        logger.info(f"Created execution plan {plan.name} by user {current_user.id}")
        return plan
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating execution plan: {e}")
        raise HTTPException(status_code=500, detail="Failed to create execution plan")


@router.put("/execution-plans/{plan_id}", response_model=ExecutionPlanResponse)
async def update_execution_plan(
    plan_id: str,
    plan_data: ExecutionPlanUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update an execution plan (admin only)"""
    try:
        plan = db.query(ExecutionPlan).filter(ExecutionPlan.id == plan_id).first()
        if not plan:
            raise HTTPException(status_code=404, detail="Execution plan not found")
        
        # Update fields
        if plan_data.name is not None:
            # Check if new name conflicts
            existing = db.query(ExecutionPlan).filter(
                ExecutionPlan.name == plan_data.name,
                ExecutionPlan.id != plan_id
            ).first()
            if existing:
                raise HTTPException(status_code=400, detail="Execution plan name already exists")
            plan.name = plan_data.name
        
        if plan_data.description is not None:
            plan.description = plan_data.description
        
        if plan_data.version is not None:
            plan.version = plan_data.version
        
        if plan_data.steps is not None:
            # Validate steps format
            if not isinstance(plan_data.steps, list):
                raise HTTPException(status_code=400, detail="Steps must be a list")
            
            for step in plan_data.steps:
                if not isinstance(step, dict) or "step" not in step:
                    raise HTTPException(status_code=400, detail="Each step must have a 'step' field")
            
            plan.steps = plan_data.steps
        
        if plan_data.global_config is not None:
            plan.global_config = plan_data.global_config
        
        if plan_data.is_active is not None:
            plan.is_active = plan_data.is_active
        
        db.commit()
        db.refresh(plan)
        
        logger.info(f"Updated execution plan {plan.name} by user {current_user.id}")
        return plan
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating execution plan: {e}")
        raise HTTPException(status_code=500, detail="Failed to update execution plan")


@router.delete("/execution-plans/{plan_id}")
async def delete_execution_plan(
    plan_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete an execution plan (admin only)"""
    try:
        plan = db.query(ExecutionPlan).filter(ExecutionPlan.id == plan_id).first()
        if not plan:
            raise HTTPException(status_code=404, detail="Execution plan not found")
        
        # Check if plan is being used by active sessions
        active_sessions = db.query(AgentSession).filter(
            AgentSession.execution_plan_id == plan_id,
            AgentSession.status.in_([SessionStatus.PENDING, SessionStatus.RUNNING])
        ).count()
        
        if active_sessions > 0:
            raise HTTPException(
                status_code=400, 
                detail=f"Cannot delete execution plan with {active_sessions} active sessions"
            )
        
        # Soft delete by marking as inactive
        plan.is_active = False
        db.commit()
        
        logger.info(f"Deleted execution plan {plan.name} by user {current_user.id}")
        return {"message": "Execution plan deleted successfully"}
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting execution plan: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete execution plan")


@router.get("/sessions", response_model=List[AgentSessionResponse])
async def list_agent_sessions(
    status: Optional[SessionStatus] = None,
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """List AI agent sessions for current tenant"""
    set_tenant_context(db, str(current_user.tenant_id))
    
    query = db.query(AgentSession).filter(AgentSession.tenant_id == current_user.tenant_id)
    
    if status:
        query = query.filter(AgentSession.status == status)
    
    sessions = query.order_by(AgentSession.created_at.desc()).offset(offset).limit(limit).all()
    return sessions


@router.get("/sessions/{session_id}", response_model=AgentSessionResponse)
async def get_agent_session(
    session_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific AI agent session"""
    set_tenant_context(db, str(current_user.tenant_id))
    
    session = db.query(AgentSession).filter(
        AgentSession.id == session_id,
        AgentSession.tenant_id == current_user.tenant_id
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return session


@router.post("/sessions", response_model=Dict[str, Any])
async def create_agent_session(
    session_data: AgentSessionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create and start a new AI agent session"""
    try:
        # Validate execution plan exists
        plan = db.query(ExecutionPlan).filter(
            ExecutionPlan.name == session_data.execution_plan_name,
            ExecutionPlan.is_active == True
        ).first()
        
        if not plan:
            raise HTTPException(status_code=400, detail="Execution plan not found or inactive")
        
        # Start the AI agent task
        task = ai_agent_process_invoice_task.delay(
            tenant_id=str(current_user.tenant_id),
            execution_plan_name=session_data.execution_plan_name,
            invoice_unique_id=session_data.invoice_unique_id,
            source_type=session_data.source_type,
            source_metadata=session_data.source_metadata
        )
        
        return {
            "message": "AI agent session started",
            "task_id": task.id,
            "execution_plan": session_data.execution_plan_name,
            "invoice_id": session_data.invoice_unique_id
        }
        
    except Exception as e:
        logger.error(f"Error creating AI agent session: {e}")
        raise HTTPException(status_code=500, detail="Failed to create session")


@router.post("/sessions/{session_id}/retry")
async def retry_agent_session(
    session_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Retry a failed AI agent session"""
    set_tenant_context(db, str(current_user.tenant_id))
    
    session = db.query(AgentSession).filter(
        AgentSession.id == session_id,
        AgentSession.tenant_id == current_user.tenant_id
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    if session.status != SessionStatus.FAILED:
        raise HTTPException(status_code=400, detail="Only failed sessions can be retried")
    
    # Start retry task
    task = retry_failed_session_task.delay(session_id)
    
    return {
        "message": "Session retry started",
        "task_id": task.id,
        "session_id": session_id
    }


@router.get("/sessions/{session_id}/steps", response_model=List[ExecutionStepResponse])
async def get_session_steps(
    session_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get execution steps for a session"""
    set_tenant_context(db, str(current_user.tenant_id))
    
    session = db.query(AgentSession).filter(
        AgentSession.id == session_id,
        AgentSession.tenant_id == current_user.tenant_id
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    steps = db.query(ExecutionStep).filter(
        ExecutionStep.session_id == session_id
    ).order_by(ExecutionStep.step_index).all()
    
    return steps


@router.get("/sessions/{session_id}/thoughts", response_model=List[ThoughtChainResponse])
async def get_session_thoughts(
    session_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get thought chain for a session"""
    set_tenant_context(db, str(current_user.tenant_id))
    
    session = db.query(AgentSession).filter(
        AgentSession.id == session_id,
        AgentSession.tenant_id == current_user.tenant_id
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    thoughts = db.query(ThoughtChain).filter(
        ThoughtChain.session_id == session_id
    ).order_by(ThoughtChain.sequence_number).all()
    
    return thoughts


@router.get("/sessions/{session_id}/tool-results", response_model=List[ToolResultResponse])
async def get_session_tool_results(
    session_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get tool results for a session"""
    set_tenant_context(db, str(current_user.tenant_id))
    
    session = db.query(AgentSession).filter(
        AgentSession.id == session_id,
        AgentSession.tenant_id == current_user.tenant_id
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    tool_results = db.query(ToolResult).filter(
        ToolResult.session_id == session_id
    ).order_by(ToolResult.created_at).all()
    
    return tool_results


@router.get("/metrics", response_model=SessionMetrics)
async def get_session_metrics(
    days: int = 30,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive metrics for AI agent sessions"""
    from app.services.duplicate_detection import DuplicateDetectionService
    from datetime import datetime, timedelta

    set_tenant_context(db, str(current_user.tenant_id))

    # Get session summary
    cutoff_date = datetime.utcnow() - timedelta(days=days)

    sessions = db.query(AgentSession).filter(
        AgentSession.tenant_id == current_user.tenant_id,
        AgentSession.created_at >= cutoff_date
    ).all()

    session_summary = {
        "total_sessions": len(sessions),
        "pending_sessions": len([s for s in sessions if s.status == SessionStatus.PENDING]),
        "running_sessions": len([s for s in sessions if s.status == SessionStatus.RUNNING]),
        "completed_sessions": len([s for s in sessions if s.status == SessionStatus.COMPLETED]),
        "failed_sessions": len([s for s in sessions if s.status == SessionStatus.FAILED]),
        "sessions_requiring_review": len([s for s in sessions if s.requires_human_review])
    }

    # Get execution plan performance
    plan_performance = {}
    for session in sessions:
        plan_name = session.execution_plan.name if session.execution_plan else "unknown"
        if plan_name not in plan_performance:
            plan_performance[plan_name] = {
                "total": 0,
                "successful": 0,
                "failed": 0,
                "execution_times": []
            }

        plan_performance[plan_name]["total"] += 1

        if session.status == SessionStatus.COMPLETED:
            plan_performance[plan_name]["successful"] += 1

            if session.started_at and session.completed_at:
                execution_time = (session.completed_at - session.started_at).total_seconds() / 60
                plan_performance[plan_name]["execution_times"].append(execution_time)
        elif session.status == SessionStatus.FAILED:
            plan_performance[plan_name]["failed"] += 1

    execution_plan_performance = []
    for plan_name, stats in plan_performance.items():
        avg_time = sum(stats["execution_times"]) / len(stats["execution_times"]) if stats["execution_times"] else None
        success_rate = stats["successful"] / stats["total"] if stats["total"] > 0 else 0

        execution_plan_performance.append({
            "plan_name": plan_name,
            "total_executions": stats["total"],
            "successful_executions": stats["successful"],
            "failed_executions": stats["failed"],
            "average_execution_time_minutes": avg_time,
            "success_rate": success_rate
        })

    # Get tool performance
    tool_results = db.query(ToolResult).join(AgentSession).filter(
        AgentSession.tenant_id == current_user.tenant_id,
        AgentSession.created_at >= cutoff_date
    ).all()

    tool_performance = {}
    for result in tool_results:
        tool_name = result.tool_name
        if tool_name not in tool_performance:
            tool_performance[tool_name] = {
                "total": 0,
                "successful": 0,
                "failed": 0,
                "execution_times": []
            }

        tool_performance[tool_name]["total"] += 1

        if result.success:
            tool_performance[tool_name]["successful"] += 1
        else:
            tool_performance[tool_name]["failed"] += 1

        if result.execution_time_ms:
            tool_performance[tool_name]["execution_times"].append(result.execution_time_ms)

    tool_performance_list = []
    for tool_name, stats in tool_performance.items():
        avg_time = sum(stats["execution_times"]) / len(stats["execution_times"]) if stats["execution_times"] else None
        success_rate = stats["successful"] / stats["total"] if stats["total"] > 0 else 0

        tool_performance_list.append({
            "tool_name": tool_name,
            "total_executions": stats["total"],
            "successful_executions": stats["successful"],
            "failed_executions": stats["failed"],
            "average_execution_time_ms": avg_time,
            "success_rate": success_rate
        })

    # Get recent failures
    recent_failures = db.query(AgentSession).filter(
        AgentSession.tenant_id == current_user.tenant_id,
        AgentSession.status == SessionStatus.FAILED,
        AgentSession.created_at >= cutoff_date
    ).order_by(AgentSession.created_at.desc()).limit(10).all()

    return {
        "session_summary": session_summary,
        "execution_plan_performance": execution_plan_performance,
        "tool_performance": tool_performance_list,
        "recent_failures": recent_failures
    }


@router.get("/sessions/{session_id}/detail", response_model=SessionDetailResponse)
async def get_session_detail(
    session_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get detailed session information including all related data"""
    set_tenant_context(db, str(current_user.tenant_id))

    session = db.query(AgentSession).filter(
        AgentSession.id == session_id,
        AgentSession.tenant_id == current_user.tenant_id
    ).first()

    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # Get all related data
    steps = db.query(ExecutionStep).filter(
        ExecutionStep.session_id == session_id
    ).order_by(ExecutionStep.step_index).all()

    tool_results = db.query(ToolResult).filter(
        ToolResult.session_id == session_id
    ).order_by(ToolResult.created_at).all()

    thoughts = db.query(ThoughtChain).filter(
        ThoughtChain.session_id == session_id
    ).order_by(ThoughtChain.sequence_number).all()

    # Create thought summary
    thought_types = {}
    key_decisions = []
    errors_encountered = []
    execution_timeline = []

    for thought in thoughts:
        # Count by type
        thought_type = thought.thought_type
        thought_types[thought_type] = thought_types.get(thought_type, 0) + 1

        # Extract key decisions
        if thought_type == "decision":
            key_decisions.append({
                "sequence": thought.sequence_number,
                "content": thought.content,
                "timestamp": thought.created_at.isoformat()
            })

        # Extract errors
        if "error" in thought.content.get("message", "").lower():
            errors_encountered.append({
                "sequence": thought.sequence_number,
                "content": thought.content,
                "timestamp": thought.created_at.isoformat()
            })

        # Build timeline
        execution_timeline.append({
            "sequence": thought.sequence_number,
            "type": thought_type,
            "message": thought.content.get("message", ""),
            "timestamp": thought.created_at.isoformat()
        })

    thought_summary = {
        "session_id": session_id,
        "total_thoughts": len(thoughts),
        "thought_types": thought_types,
        "key_decisions": key_decisions,
        "errors_encountered": errors_encountered,
        "execution_timeline": execution_timeline
    }

    return {
        "session": session,
        "execution_plan": session.execution_plan,
        "steps": steps,
        "tool_results": tool_results,
        "thought_summary": thought_summary
    }


@router.get("/tools")
async def list_available_tools(
    current_user: User = Depends(get_current_user)
):
    """List all available AI agent tools"""
    from app.services.ai_agent_tools import ToolRegistry

    registry = ToolRegistry()
    # This would need to be implemented in the registry
    # For now, return the known tools

    tools = [
        {
            "name": "get_invoice",
            "description": "Fetch invoice from configured integration (Fortnox, eEkonomi, HTTP, etc.)",
            "category": "data_retrieval"
        },
        {
            "name": "extract_content",
            "description": "Determine file format and extract invoice content appropriately",
            "category": "content_processing"
        },
        {
            "name": "web_search",
            "description": "Enrich invoice context by searching for supplier and product information",
            "category": "enrichment"
        },
        {
            "name": "rag_search",
            "description": "Use RAG search against vector database to determine appropriate accounting accounts",
            "category": "analysis"
        },
        {
            "name": "book_invoice",
            "description": "Book the invoice using the determined accounting entries",
            "category": "action"
        }
    ]

    return {"tools": tools}


@router.post("/sessions/{session_id}/cancel")
async def cancel_session(
    session_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Cancel a running AI agent session"""
    set_tenant_context(db, str(current_user.tenant_id))

    session = db.query(AgentSession).filter(
        AgentSession.id == session_id,
        AgentSession.tenant_id == current_user.tenant_id
    ).first()

    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    if session.status not in [SessionStatus.PENDING, SessionStatus.RUNNING]:
        raise HTTPException(status_code=400, detail="Only pending or running sessions can be cancelled")

    # Update session status
    session.status = SessionStatus.CANCELLED
    session.completed_at = datetime.utcnow()

    # Cancel any pending steps
    for step in session.execution_steps:
        if step.status == StepStatus.PENDING:
            step.status = StepStatus.SKIPPED

    db.commit()

    return {"message": "Session cancelled successfully", "session_id": session_id}
