"""Update action items for AI agent integration

Revision ID: 012
Revises: 011
Create Date: 2025-01-03 11:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '012'
down_revision = '011'
branch_labels = None
depends_on = None


def upgrade():
    # Add agent_session_id column to action_items table
    op.add_column('action_items', sa.Column('agent_session_id', postgresql.UUID(as_uuid=True), nullable=True))
    
    # Add foreign key constraint
    op.create_foreign_key(
        'fk_action_items_agent_session_id',
        'action_items', 
        'agent_sessions',
        ['agent_session_id'], 
        ['id']
    )
    
    # Add index for better query performance
    op.create_index(
        'ix_action_items_agent_session_id',
        'action_items',
        ['agent_session_id'],
        unique=False
    )


def downgrade():
    # Drop index
    op.drop_index('ix_action_items_agent_session_id', table_name='action_items')
    
    # Drop foreign key constraint
    op.drop_constraint('fk_action_items_agent_session_id', 'action_items', type_='foreignkey')
    
    # Drop column
    op.drop_column('action_items', 'agent_session_id')
