import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy.orm import Session
from datetime import datetime
import uuid

from app.services.ai_agent_orchestrator import AIAgentOrchestrator, DuplicateInvoiceError
from app.models.agent_session import (
    AgentSession, ExecutionPlan, ExecutionStep, ToolResult, ThoughtChain,
    SessionStatus, StepStatus
)
from app.models.tenant import Tenant


class TestAIAgentOrchestrator:
    """Test cases for AI Agent Orchestrator"""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def mock_tenant(self):
        """Mock tenant"""
        tenant = Mock(spec=Tenant)
        tenant.id = uuid.uuid4()
        return tenant
    
    @pytest.fixture
    def mock_execution_plan(self):
        """Mock execution plan"""
        plan = Mock(spec=ExecutionPlan)
        plan.id = uuid.uuid4()
        plan.name = "test_plan"
        plan.steps = [
            {"step": "get_invoice", "description": "Test step 1"},
            {"step": "extract_content", "description": "Test step 2"}
        ]
        return plan
    
    @pytest.fixture
    def orchestrator(self, mock_db):
        """Create orchestrator instance"""
        return AIAgentOrchestrator(mock_db)
    
    @pytest.mark.asyncio
    async def test_create_session_success(self, orchestrator, mock_db, mock_execution_plan):
        """Test successful session creation"""
        # Setup mocks
        mock_db.query.return_value.filter.return_value.first.return_value = mock_execution_plan
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.refresh = Mock()
        
        # Mock duplicate check
        orchestrator.duplicate_service.check_duplicate_invoice = Mock(return_value=None)
        
        # Create session
        session = await orchestrator.create_session(
            tenant_id=str(uuid.uuid4()),
            execution_plan_name="test_plan",
            invoice_unique_id="test_invoice_123",
            source_type="erp"
        )
        
        # Verify session was created
        assert isinstance(session, AgentSession)
        assert session.status == SessionStatus.PENDING
        assert session.invoice_unique_id == "test_invoice_123"
        assert session.source_type == "erp"
        
        # Verify database operations
        mock_db.add.assert_called()
        mock_db.commit.assert_called()
    
    @pytest.mark.asyncio
    async def test_create_session_duplicate_detection(self, orchestrator, mock_db):
        """Test duplicate detection during session creation"""
        # Setup duplicate session
        existing_session = Mock(spec=AgentSession)
        existing_session.id = uuid.uuid4()
        existing_session.status = SessionStatus.COMPLETED
        existing_session.source_type = "erp"
        
        orchestrator.duplicate_service.check_duplicate_invoice = Mock(return_value=existing_session)
        
        # Attempt to create session should raise duplicate error
        with pytest.raises(DuplicateInvoiceError) as exc_info:
            await orchestrator.create_session(
                tenant_id=str(uuid.uuid4()),
                execution_plan_name="test_plan",
                invoice_unique_id="duplicate_invoice",
                source_type="erp"
            )
        
        assert "duplicate" in str(exc_info.value).lower()
        assert str(existing_session.id) in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_execute_session_success(self, orchestrator, mock_db):
        """Test successful session execution"""
        # Setup session with steps
        session_id = str(uuid.uuid4())
        session = Mock(spec=AgentSession)
        session.id = session_id
        session.tenant_id = uuid.uuid4()
        session.status = SessionStatus.PENDING
        session.current_step_index = 0
        session.execution_steps = []
        
        # Create mock steps
        step1 = Mock(spec=ExecutionStep)
        step1.id = uuid.uuid4()
        step1.step_name = "get_invoice"
        step1.step_config = {"step": "get_invoice"}
        step1.step_index = 0
        step1.status = StepStatus.PENDING
        
        session.execution_steps = [step1]
        
        mock_db.query.return_value.filter.return_value.first.return_value = session
        
        # Mock tool execution
        mock_tool = Mock()
        mock_tool.execute = AsyncMock(return_value={"success": True, "data": "test"})
        
        orchestrator.tool_registry.get_tool = Mock(return_value=Mock(return_value=mock_tool))
        orchestrator._prepare_step_input = AsyncMock(return_value={"test": "input"})
        orchestrator._log_thought = AsyncMock()
        
        # Execute session
        result_session = await orchestrator.execute_session(session_id)
        
        # Verify execution
        assert result_session.status == SessionStatus.COMPLETED
        assert result_session.current_step_index == 1  # Should advance past the step
    
    @pytest.mark.asyncio
    async def test_execute_step_success(self, orchestrator, mock_db):
        """Test successful step execution"""
        # Setup session and step
        session = Mock(spec=AgentSession)
        session.id = uuid.uuid4()
        session.tenant_id = uuid.uuid4()
        
        step = Mock(spec=ExecutionStep)
        step.id = uuid.uuid4()
        step.step_name = "test_tool"
        step.step_config = {"step": "test_tool"}
        step.step_index = 0
        step.status = StepStatus.PENDING
        
        # Mock tool
        mock_tool = Mock()
        mock_tool.execute = AsyncMock(return_value={"success": True, "result": "test_output"})
        
        orchestrator.tool_registry.get_tool = Mock(return_value=Mock(return_value=mock_tool))
        orchestrator._prepare_step_input = AsyncMock(return_value={"input": "test"})
        orchestrator._log_thought = AsyncMock()
        
        # Execute step
        await orchestrator._execute_step(session, step)
        
        # Verify step completion
        assert step.status == StepStatus.COMPLETED
        assert step.output_data == {"success": True, "result": "test_output"}
        
        # Verify tool result was created
        mock_db.add.assert_called()
        mock_db.commit.assert_called()
    
    @pytest.mark.asyncio
    async def test_execute_step_failure(self, orchestrator, mock_db):
        """Test step execution failure"""
        # Setup session and step
        session = Mock(spec=AgentSession)
        session.id = uuid.uuid4()
        session.tenant_id = uuid.uuid4()
        
        step = Mock(spec=ExecutionStep)
        step.id = uuid.uuid4()
        step.step_name = "failing_tool"
        step.step_config = {"step": "failing_tool"}
        step.step_index = 0
        step.status = StepStatus.PENDING
        
        # Mock tool that fails
        mock_tool = Mock()
        mock_tool.execute = AsyncMock(side_effect=Exception("Tool execution failed"))
        
        orchestrator.tool_registry.get_tool = Mock(return_value=Mock(return_value=mock_tool))
        orchestrator._prepare_step_input = AsyncMock(return_value={"input": "test"})
        orchestrator._log_thought = AsyncMock()
        
        # Execute step should raise exception
        with pytest.raises(Exception) as exc_info:
            await orchestrator._execute_step(session, step)
        
        assert "Tool execution failed" in str(exc_info.value)
        
        # Verify step marked as failed
        assert step.status == StepStatus.FAILED
        assert step.error_message == "Tool execution failed"
    
    def test_prepare_step_input(self, orchestrator):
        """Test step input preparation"""
        # Setup session with previous steps
        session = Mock(spec=AgentSession)
        session.id = uuid.uuid4()
        session.invoice_unique_id = "test_invoice"
        session.source_type = "manual"
        session.source_metadata = {"file_path": "/test/path"}
        
        # Create previous step with output
        prev_step = Mock(spec=ExecutionStep)
        prev_step.step_name = "get_invoice"
        prev_step.status = StepStatus.COMPLETED
        prev_step.output_data = {"invoice_data": {"id": "123"}}
        
        current_step = Mock(spec=ExecutionStep)
        current_step.step_index = 1
        current_step.step_config = {"step": "extract_content"}
        
        session.execution_steps = [prev_step, current_step]
        
        # Prepare input
        result = asyncio.run(orchestrator._prepare_step_input(session, current_step))
        
        # Verify input structure
        assert "session_id" in result
        assert "step_config" in result
        assert "session_context" in result
        assert "previous_results" in result
        
        # Verify previous results included
        assert "get_invoice" in result["previous_results"]
        assert result["previous_results"]["get_invoice"] == {"invoice_data": {"id": "123"}}
        
        # Verify session context
        assert result["session_context"]["invoice_unique_id"] == "test_invoice"
        assert result["session_context"]["source_type"] == "manual"
    
    @pytest.mark.asyncio
    async def test_handle_step_error_with_retry(self, orchestrator, mock_db):
        """Test step error handling with retry"""
        # Setup session
        session = Mock(spec=AgentSession)
        session.id = uuid.uuid4()
        session.retry_count = 0
        session.max_retries = 1
        session.execution_steps = []
        
        step = Mock(spec=ExecutionStep)
        step.id = uuid.uuid4()
        step.step_name = "test_step"
        
        orchestrator._log_thought = AsyncMock()
        
        # Handle error (should retry)
        await orchestrator._handle_step_error(session, step, Exception("Test error"))
        
        # Verify retry was set up
        assert session.retry_count == 1
        assert session.current_step_index == 0  # Reset to beginning
    
    @pytest.mark.asyncio
    async def test_handle_step_error_max_retries(self, orchestrator, mock_db):
        """Test step error handling when max retries reached"""
        # Setup session at max retries
        session = Mock(spec=AgentSession)
        session.id = uuid.uuid4()
        session.retry_count = 1
        session.max_retries = 1
        
        step = Mock(spec=ExecutionStep)
        step.id = uuid.uuid4()
        step.step_name = "test_step"
        
        orchestrator._log_thought = AsyncMock()
        
        # Handle error (should fail session)
        await orchestrator._handle_step_error(session, step, Exception("Test error"))
        
        # Verify session marked as failed
        assert session.status == SessionStatus.FAILED
        assert session.requires_human_review == True
        assert "Max retries" in session.action_item_reason
    
    @pytest.mark.asyncio
    async def test_handle_duplicate_error_no_retry(self, orchestrator, mock_db):
        """Test that duplicate errors don't trigger retry"""
        # Setup session
        session = Mock(spec=AgentSession)
        session.id = uuid.uuid4()
        session.retry_count = 0
        session.max_retries = 1
        
        step = Mock(spec=ExecutionStep)
        step.id = uuid.uuid4()
        step.step_name = "test_step"
        
        orchestrator._log_thought = AsyncMock()
        
        # Handle duplicate error (should not retry)
        await orchestrator._handle_step_error(session, step, DuplicateInvoiceError("Duplicate found"))
        
        # Verify session failed immediately without retry
        assert session.status == SessionStatus.FAILED
        assert session.requires_human_review == True
        assert "Duplicate invoice detected" in session.action_item_reason
        assert session.retry_count == 0  # Should not increment
    
    @pytest.mark.asyncio
    async def test_log_thought(self, orchestrator, mock_db):
        """Test thought logging"""
        session_id = str(uuid.uuid4())
        step_id = str(uuid.uuid4())
        
        # Mock session for tenant_id
        session = Mock(spec=AgentSession)
        session.tenant_id = uuid.uuid4()
        mock_db.query.return_value.filter.return_value.first.return_value = session
        
        # Mock last thought for sequence number
        last_thought = Mock(spec=ThoughtChain)
        last_thought.sequence_number = 5
        mock_db.query.return_value.filter.return_value.order_by.return_value.first.return_value = last_thought
        
        # Log thought
        await orchestrator._log_thought(
            session_id=session_id,
            step_id=step_id,
            thought_type="reasoning",
            content={"message": "Test thought"},
            context={"test": "context"}
        )
        
        # Verify thought was added
        mock_db.add.assert_called()
        mock_db.commit.assert_called()
        
        # Get the added thought
        added_thought = mock_db.add.call_args[0][0]
        assert added_thought.sequence_number == 6  # Should increment
        assert added_thought.thought_type == "reasoning"
        assert added_thought.content == {"message": "Test thought"}


if __name__ == "__main__":
    pytest.main([__file__])
