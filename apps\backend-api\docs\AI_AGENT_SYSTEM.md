# AI Agent System Documentation

## Overview

The AI Agent System is a comprehensive orchestration framework for automating invoice processing workflows. It replaces the traditional linear processing approach with an intelligent, configurable, and observable AI-driven system.

## Key Features

- **Configurable Execution Plans**: Define custom workflows with step-by-step execution
- **Intelligent Duplicate Detection**: Multi-strategy duplicate detection for different source types
- **Comprehensive Logging**: Detailed thought chains and execution tracking
- **Human-in-the-Loop (HITL)**: Automatic action item creation for failed sessions
- **Retry Mechanisms**: Configurable retry logic with error handling
- **Tool-based Architecture**: Modular tools for different processing steps
- **Multi-tenant Support**: Full tenant isolation with RLS policies

## Architecture

### Core Components

1. **AI Agent Orchestrator** (`AIAgentOrchestrator`)
   - Main orchestration engine
   - Manages session lifecycle
   - Handles error recovery and retries

2. **Tool Registry** (`ToolRegistry`)
   - Manages available processing tools
   - Provides tool discovery and instantiation

3. **Duplicate Detection Service** (`DuplicateDetectionService`)
   - Multi-strategy duplicate detection
   - Content-based, metadata-based, and exact ID matching

4. **Action Item Service** (`AIAgentActionItemService`)
   - Creates and manages action items for failed sessions
   - Integrates with notification system

### Database Schema

#### Core Tables

- **execution_plans**: Configurable workflow definitions
- **agent_sessions**: Individual execution instances
- **execution_steps**: Step-by-step execution tracking
- **tool_results**: Results from tool executions
- **thought_chains**: AI agent reasoning and decision logs
- **action_items**: Extended to support AI agent sessions

## Execution Flow

### Standard Invoice Processing Workflow

1. **get_invoice**: Fetch invoice from configured source (ERP, HTTP, manual)
2. **extract_content**: Determine file format and extract content using OCR/LLM
3. **web_search**: Enrich context with supplier and product information
4. **rag_search**: Use vector similarity search to determine accounting codes
5. **book_invoice**: Execute the booking with confidence validation

### Session Lifecycle

```
PENDING → RUNNING → COMPLETED/FAILED
    ↓         ↓
    ↓    (retry logic)
    ↓         ↓
    ↓    PENDING (retry)
    ↓
(duplicate check)
    ↓
  FAILED
```

## Tools

### Available Tools

1. **GetInvoiceTool**
   - Fetches invoices from various sources
   - Handles ERP integrations, HTTP endpoints, and manual uploads
   - Supports different authentication methods

2. **ExtractContentTool**
   - Determines file format (PDF, image)
   - Extracts text using OCR services
   - Structures content using LLM processing

3. **WebSearchTool**
   - Enriches context with external data
   - Searches for supplier and product information
   - Uses Google Custom Search API

4. **RAGSearchTool**
   - Performs vector similarity search
   - Finds similar historical invoices
   - Generates accounting suggestions using LLM

5. **BookInvoiceTool**
   - Validates confidence levels
   - Creates accounting entries
   - Integrates with accounting systems

### Tool Interface

```python
class BaseTool(ABC):
    @abstractmethod
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the tool with given input data"""
        pass
```

## Duplicate Detection

### Detection Strategies

1. **Exact ID Match** (ERP sources)
   - Matches on unique invoice IDs
   - Prevents reprocessing of same invoice

2. **Content-based Detection** (HTTP/Manual sources)
   - File content hashing
   - File size + filename matching
   - Prevents duplicate file uploads

3. **Metadata-based Detection** (All sources)
   - Supplier + invoice number matching
   - Supplier + amount + date matching
   - Business logic duplicate detection

### Configuration

```python
# Duplicate detection is automatic based on source type
# ERP: Uses exact ID matching
# HTTP: Uses content + metadata matching  
# Manual: Uses content + metadata matching
```

## Error Handling and Recovery

### Retry Logic

- Configurable retry attempts (default: 1)
- Automatic retry for transient errors
- No retry for duplicate detection errors
- Session state reset on retry

### Action Items

Failed sessions automatically create action items with:
- Descriptive titles and detailed descriptions
- Priority based on failure type
- Category classification (error, validation, review)
- Integration with notification system

### Failure Categories

1. **Duplicate Detection**: Medium priority, validation category
2. **System Errors**: High priority, error category
3. **Low Confidence**: Low priority, review category
4. **Tool Failures**: High priority, error category

## Configuration

### Execution Plans

Execution plans are stored in the database and can be configured via API:

```json
{
  "name": "invoice_processing_v1",
  "description": "Standard invoice processing workflow",
  "version": "1.0",
  "steps": [
    {"step": "get_invoice", "description": "Fetch invoice"},
    {"step": "extract_content", "description": "Extract content"},
    {"step": "web_search", "description": "Enrich context"},
    {"step": "rag_search", "description": "Find accounting codes"},
    {"step": "book_invoice", "description": "Book invoice"}
  ],
  "global_config": {
    "confidence_threshold": 0.8,
    "max_retries": 1
  }
}
```

### Environment Variables

```bash
# Google Search API (optional)
GOOGLE_SEARCH_API_KEY=your_api_key
GOOGLE_SEARCH_ENGINE_ID=your_engine_id

# LLM Configuration
OPENAI_API_KEY=your_openai_key
LLM_PROVIDER=openai

# Database
DATABASE_URL=postgresql://...
```

## API Endpoints

### Execution Plans

- `GET /api/v1/ai-agent/execution-plans` - List plans
- `POST /api/v1/ai-agent/execution-plans` - Create plan (admin)
- `PUT /api/v1/ai-agent/execution-plans/{id}` - Update plan (admin)
- `DELETE /api/v1/ai-agent/execution-plans/{id}` - Delete plan (admin)

### Sessions

- `GET /api/v1/ai-agent/sessions` - List sessions
- `GET /api/v1/ai-agent/sessions/{id}` - Get session details
- `POST /api/v1/ai-agent/sessions` - Create session
- `POST /api/v1/ai-agent/sessions/{id}/retry` - Retry failed session
- `POST /api/v1/ai-agent/sessions/{id}/cancel` - Cancel session

### Monitoring

- `GET /api/v1/ai-agent/metrics` - Get performance metrics
- `GET /api/v1/ai-agent/sessions/{id}/detail` - Detailed session view
- `GET /api/v1/ai-agent/tools` - List available tools

## Monitoring and Observability

### Thought Chains

Every session maintains a detailed thought chain with:
- Reasoning steps
- Decision points
- Observations
- Error details

### Metrics

- Session success/failure rates
- Tool performance statistics
- Execution time analytics
- Duplicate detection statistics

### Logging

Structured logging at multiple levels:
- Session lifecycle events
- Tool execution results
- Error conditions
- Performance metrics

## System Architecture

### Complete AI Agent Integration

The system has fully migrated to AI Agent orchestration:
- All invoice processing uses `ai_agent_process_invoice_task`
- Legacy processing code has been removed
- All workflows go through AI Agent orchestrator

### System Components

1. AI Agent Orchestrator handles all processing
2. Execution plans define workflows
3. Tools perform specific tasks
4. Sessions track complete lifecycle
5. Monitoring provides full observability

## Testing

### Test Coverage

- Unit tests for all core components
- Integration tests for end-to-end workflows
- Duplicate detection test scenarios
- Error handling and recovery tests

### Running Tests

```bash
# Run all AI Agent tests
pytest tests/test_ai_agent_*.py

# Run specific test files
pytest tests/test_ai_agent_orchestrator.py
pytest tests/test_duplicate_detection.py
pytest tests/test_ai_agent_integration.py
```

## Troubleshooting

### Common Issues

1. **Tool Execution Failures**
   - Check tool configuration
   - Verify API keys and credentials
   - Review error logs in thought chains

2. **Duplicate Detection False Positives**
   - Review detection strategies
   - Adjust metadata extraction
   - Check content hashing

3. **Performance Issues**
   - Monitor tool execution times
   - Check database query performance
   - Review vector search efficiency

### Debug Information

- Session thought chains provide detailed execution logs
- Tool results contain input/output data
- Error messages include stack traces
- Performance metrics track execution times

## Future Enhancements

- Advanced AI reasoning capabilities
- Custom tool development framework
- Real-time session monitoring dashboard
- Machine learning-based confidence scoring
- Integration with more ERP systems
- Advanced duplicate detection algorithms
