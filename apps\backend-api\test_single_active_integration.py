#!/usr/bin/env python3
"""
Test script to verify that only one integration can be active per tenant.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.database import SessionLocal, set_tenant_context
from app.models.integration import InvoiceIntegration
from app.models.tenant import Tenant
from uuid import uuid4
import json

def test_single_active_integration():
    """Test that only one integration can be active per tenant"""
    db = SessionLocal()
    
    try:
        # Find an existing tenant or create a test one
        tenant = db.query(Tenant).filter(Tenant.is_active == True).first()
        if not tenant:
            print("❌ No active tenant found. Please ensure there's at least one active tenant.")
            return False
        
        print(f"🧪 Testing with tenant: {tenant.id}")
        
        # Set tenant context
        set_tenant_context(db, str(tenant.id))
        
        # Clean up any existing integrations for this tenant
        db.query(InvoiceIntegration).filter(
            InvoiceIntegration.tenant_id == tenant.id
        ).delete()
        db.commit()
        
        # Create first integration (active)
        integration1 = InvoiceIntegration(
            tenant_id=tenant.id,
            integration_type="HTTP",
            configuration=json.dumps({"base_url": "https://example1.com"}),
            name="Test Integration 1",
            is_active=True
        )
        db.add(integration1)
        db.commit()
        db.refresh(integration1)

        print(f"✅ Created first integration: {integration1.id} (active: {integration1.is_active})")

        # Create second integration (active) - this should deactivate the first one
        # First, simulate the API logic: deactivate existing active integrations
        db.query(InvoiceIntegration).filter(
            InvoiceIntegration.tenant_id == tenant.id,
            InvoiceIntegration.is_active == True
        ).update({"is_active": False})

        integration2 = InvoiceIntegration(
            tenant_id=tenant.id,
            integration_type="HTTP",
            configuration=json.dumps({"base_url": "https://example2.com"}),
            name="Test Integration 2",
            is_active=True
        )
        db.add(integration2)
        db.commit()
        db.refresh(integration2)
        
        print(f"✅ Created second integration: {integration2.id} (active: {integration2.is_active})")
        
        # Check that only one integration is active
        active_integrations = db.query(InvoiceIntegration).filter(
            InvoiceIntegration.tenant_id == tenant.id,
            InvoiceIntegration.is_active == True
        ).all()
        
        print(f"📊 Active integrations count: {len(active_integrations)}")
        
        if len(active_integrations) == 1:
            active_integration = active_integrations[0]
            print(f"✅ SUCCESS: Only one integration is active: {active_integration.id} ({active_integration.name})")
            
            # Verify it's the second one (most recently created)
            if active_integration.id == integration2.id:
                print("✅ SUCCESS: The most recently created integration is the active one")
                return True
            else:
                print("❌ FAIL: The wrong integration is active")
                return False
        else:
            print(f"❌ FAIL: Expected 1 active integration, found {len(active_integrations)}")
            for integration in active_integrations:
                print(f"   - {integration.id} ({integration.name})")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False
    finally:
        # Clean up test data
        try:
            db.query(InvoiceIntegration).filter(
                InvoiceIntegration.tenant_id == tenant.id,
                InvoiceIntegration.name.like("Test Integration %")
            ).delete()
            db.commit()
            print("🧹 Cleaned up test data")
        except:
            pass
        db.close()

if __name__ == "__main__":
    print("🧪 Testing Single Active Integration Logic")
    print("=" * 50)
    
    success = test_single_active_integration()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
        sys.exit(1)
