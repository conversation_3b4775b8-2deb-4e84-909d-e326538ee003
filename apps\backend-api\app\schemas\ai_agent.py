from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

from app.models.agent_session import SessionStatus, StepStatus


class ExecutionPlanCreate(BaseModel):
    name: str = Field(..., description="Unique name for the execution plan")
    description: Optional[str] = Field(None, description="Description of the execution plan")
    version: str = Field("1.0", description="Version of the execution plan")
    steps: List[Dict[str, Any]] = Field(..., description="List of execution steps")
    global_config: Optional[Dict[str, Any]] = Field(None, description="Global configuration for the plan")


class ExecutionPlanUpdate(BaseModel):
    name: Optional[str] = Field(None, description="Unique name for the execution plan")
    description: Optional[str] = Field(None, description="Description of the execution plan")
    version: Optional[str] = Field(None, description="Version of the execution plan")
    steps: Optional[List[Dict[str, Any]]] = Field(None, description="List of execution steps")
    global_config: Optional[Dict[str, Any]] = Field(None, description="Global configuration for the plan")
    is_active: Optional[bool] = Field(None, description="Whether the plan is active")


class ExecutionPlanResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    version: str
    is_active: bool
    steps: List[Dict[str, Any]]
    global_config: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AgentSessionCreate(BaseModel):
    execution_plan_name: str = Field(..., description="Name of the execution plan to use")
    invoice_unique_id: Optional[str] = Field(None, description="Unique ID of the invoice to process")
    source_type: str = Field("manual", description="Source type: manual, erp, http")
    source_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional source metadata")


class AgentSessionResponse(BaseModel):
    id: str
    tenant_id: str
    session_name: str
    status: SessionStatus
    execution_plan_id: str
    invoice_unique_id: Optional[str]
    source_type: str
    source_metadata: Optional[Dict[str, Any]]
    current_step_index: int
    retry_count: int
    max_retries: int
    requires_human_review: bool
    action_item_reason: Optional[str]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    updated_at: datetime

    class Config:
        from_attributes = True


class ExecutionStepResponse(BaseModel):
    id: str
    session_id: str
    tenant_id: str
    step_name: str
    step_index: int
    status: StepStatus
    step_config: Dict[str, Any]
    input_data: Optional[Dict[str, Any]]
    output_data: Optional[Dict[str, Any]]
    error_message: Optional[str]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]

    class Config:
        from_attributes = True


class ToolResultResponse(BaseModel):
    id: str
    session_id: str
    step_id: Optional[str]
    tenant_id: str
    tool_name: str
    tool_version: Optional[str]
    input_parameters: Dict[str, Any]
    output_data: Optional[Dict[str, Any]]
    success: bool
    error_message: Optional[str]
    execution_time_ms: Optional[int]
    created_at: datetime

    class Config:
        from_attributes = True


class ThoughtChainResponse(BaseModel):
    id: str
    session_id: str
    step_id: Optional[str]
    tenant_id: str
    sequence_number: int
    thought_type: str
    content: Dict[str, Any]
    context: Optional[Dict[str, Any]]
    created_at: datetime

    class Config:
        from_attributes = True


class SessionStatusEnum(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class StepStatusEnum(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class SessionSummary(BaseModel):
    """Summary statistics for sessions"""
    total_sessions: int
    pending_sessions: int
    running_sessions: int
    completed_sessions: int
    failed_sessions: int
    sessions_requiring_review: int


class ExecutionPlanSummary(BaseModel):
    """Summary of execution plan usage"""
    plan_name: str
    total_executions: int
    successful_executions: int
    failed_executions: int
    average_execution_time_minutes: Optional[float]
    success_rate: float


class ToolPerformance(BaseModel):
    """Performance metrics for tools"""
    tool_name: str
    total_executions: int
    successful_executions: int
    failed_executions: int
    average_execution_time_ms: Optional[float]
    success_rate: float


class SessionMetrics(BaseModel):
    """Comprehensive session metrics"""
    session_summary: SessionSummary
    execution_plan_performance: List[ExecutionPlanSummary]
    tool_performance: List[ToolPerformance]
    recent_failures: List[AgentSessionResponse]


class ThoughtChainSummary(BaseModel):
    """Summary of thought chain for display"""
    session_id: str
    total_thoughts: int
    thought_types: Dict[str, int]  # Count by type
    key_decisions: List[Dict[str, Any]]  # Important decision points
    errors_encountered: List[Dict[str, Any]]  # Error thoughts
    execution_timeline: List[Dict[str, Any]]  # Timeline of major events


class SessionDetailResponse(BaseModel):
    """Detailed session information including all related data"""
    session: AgentSessionResponse
    execution_plan: ExecutionPlanResponse
    steps: List[ExecutionStepResponse]
    tool_results: List[ToolResultResponse]
    thought_summary: ThoughtChainSummary
    
    class Config:
        from_attributes = True
