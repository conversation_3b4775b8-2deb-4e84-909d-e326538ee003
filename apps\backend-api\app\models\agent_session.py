from sqlalchemy import Column, String, DateTime, Boolean, Text, Integer, ForeignKey, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import enum

from app.database import Base


class SessionStatus(enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class StepStatus(enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class AgentSession(Base):
    """AI Agent execution session"""
    __tablename__ = "agent_sessions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    
    # Session metadata
    session_name = Column(String(255), nullable=False)
    status = Column(SQLEnum(SessionStatus), default=SessionStatus.PENDING, nullable=False)
    execution_plan_id = Column(UUID(as_uuid=True), ForeignKey("execution_plans.id"), nullable=False)
    
    # Invoice processing context
    invoice_unique_id = Column(String(255), nullable=True, index=True)  # For duplicate detection
    source_type = Column(String(50), nullable=False)  # 'erp', 'http', 'manual'
    source_metadata = Column(JSON, nullable=True)  # Additional source info
    
    # Session state
    current_step_index = Column(Integer, default=0, nullable=False)
    retry_count = Column(Integer, default=0, nullable=False)
    max_retries = Column(Integer, default=1, nullable=False)
    
    # Action item flag
    requires_human_review = Column(Boolean, default=False, nullable=False)
    action_item_reason = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    tenant = relationship("Tenant", back_populates="agent_sessions")
    execution_plan = relationship("ExecutionPlan", back_populates="sessions")
    execution_steps = relationship("ExecutionStep", back_populates="session", cascade="all, delete-orphan")
    tool_results = relationship("ToolResult", back_populates="session", cascade="all, delete-orphan")
    thought_chains = relationship("ThoughtChain", back_populates="session", cascade="all, delete-orphan")
    action_items = relationship("ActionItem", back_populates="agent_session", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<AgentSession(id={self.id}, status={self.status}, tenant_id={self.tenant_id})>"


class ExecutionPlan(Base):
    """Configurable execution plan for AI agents"""
    __tablename__ = "execution_plans"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Plan metadata
    name = Column(String(255), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    version = Column(String(50), default="1.0", nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Plan configuration
    steps = Column(JSON, nullable=False)  # List of step configurations
    global_config = Column(JSON, nullable=True)  # Global settings for the plan
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    sessions = relationship("AgentSession", back_populates="execution_plan")

    def __repr__(self):
        return f"<ExecutionPlan(id={self.id}, name={self.name}, version={self.version})>"


class ExecutionStep(Base):
    """Individual step execution within a session"""
    __tablename__ = "execution_steps"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(UUID(as_uuid=True), ForeignKey("agent_sessions.id"), nullable=False, index=True)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    
    # Step metadata
    step_name = Column(String(255), nullable=False)
    step_index = Column(Integer, nullable=False)
    status = Column(SQLEnum(StepStatus), default=StepStatus.PENDING, nullable=False)
    
    # Step configuration from execution plan
    step_config = Column(JSON, nullable=False)
    
    # Execution results
    input_data = Column(JSON, nullable=True)
    output_data = Column(JSON, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Relationships
    session = relationship("AgentSession", back_populates="execution_steps")
    tenant = relationship("Tenant")

    def __repr__(self):
        return f"<ExecutionStep(id={self.id}, step_name={self.step_name}, status={self.status})>"


class ToolResult(Base):
    """Results from tool executions"""
    __tablename__ = "tool_results"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(UUID(as_uuid=True), ForeignKey("agent_sessions.id"), nullable=False, index=True)
    step_id = Column(UUID(as_uuid=True), ForeignKey("execution_steps.id"), nullable=True, index=True)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    
    # Tool metadata
    tool_name = Column(String(255), nullable=False)
    tool_version = Column(String(50), nullable=True)
    
    # Tool execution
    input_parameters = Column(JSON, nullable=False)
    output_data = Column(JSON, nullable=True)
    success = Column(Boolean, nullable=False)
    error_message = Column(Text, nullable=True)
    execution_time_ms = Column(Integer, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    session = relationship("AgentSession", back_populates="tool_results")
    step = relationship("ExecutionStep")
    tenant = relationship("Tenant")

    def __repr__(self):
        return f"<ToolResult(id={self.id}, tool_name={self.tool_name}, success={self.success})>"


class ThoughtChain(Base):
    """AI Agent thought process and reasoning"""
    __tablename__ = "thought_chains"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(UUID(as_uuid=True), ForeignKey("agent_sessions.id"), nullable=False, index=True)
    step_id = Column(UUID(as_uuid=True), ForeignKey("execution_steps.id"), nullable=True, index=True)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    
    # Thought metadata
    sequence_number = Column(Integer, nullable=False)
    thought_type = Column(String(100), nullable=False)  # 'reasoning', 'decision', 'observation', 'plan'
    
    # Thought content
    content = Column(JSON, nullable=False)  # Structured thought data
    context = Column(JSON, nullable=True)  # Additional context data
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    session = relationship("AgentSession", back_populates="thought_chains")
    step = relationship("ExecutionStep")
    tenant = relationship("Tenant")

    def __repr__(self):
        return f"<ThoughtChain(id={self.id}, thought_type={self.thought_type}, sequence={self.sequence_number})>"
