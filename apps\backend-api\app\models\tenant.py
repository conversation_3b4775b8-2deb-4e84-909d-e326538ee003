from sqlalchemy import Column, String, Boolean, Text
from sqlalchemy.orm import relationship
from .base import BaseModel


class Tenant(BaseModel):
    """Tenant model for multi-tenancy"""
    __tablename__ = "tenants"

    name = Column(String(255), nullable=False)
    domain = Column(String(255), unique=True, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    settings = Column(Text, nullable=True)  # JSON settings
    
    # Relationships
    tenant_users = relationship("TenantUser", back_populates="tenant", cascade="all, delete-orphan")
    invoices = relationship("Invoice", back_populates="tenant", cascade="all, delete-orphan")
    action_items = relationship("ActionItem", back_populates="tenant", cascade="all, delete-orphan")
    invoice_integrations = relationship("InvoiceIntegration", back_populates="tenant", cascade="all, delete-orphan")
    agent_sessions = relationship("AgentSession", back_populates="tenant", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Tenant(id={self.id}, name='{self.name}')>"
