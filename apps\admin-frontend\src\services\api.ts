import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import {
  User,
  LoginResponse,
  TokenResponse,
  Invoice,
  ActionItem,
  TwoFASetupResponse,
  ApiError
} from '../types';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`;

      // Add tenant header if available
      const tenantId = localStorage.getItem('current_tenant_id');
      if (tenantId) {
        config.headers['X-Tenant-ID'] = tenantId;
      }

      // Debug logging
      console.log('API Request:', {
        url: config.url,
        method: config.method,
        headers: {
          'Authorization': token ? 'Bearer [REDACTED]' : 'None',
          'X-Tenant-ID': tenantId || 'None'
        }
      });
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor for token refresh
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError<ApiError>) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // Debug logging for errors
    console.error('API Error:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url,
      method: error.config?.method
    });

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }
        
        const response = await axios.post<TokenResponse>(`${API_URL}/auth/refresh`, {
          refresh_token: refreshToken
        });
        
        localStorage.setItem('access_token', response.data.access_token);
        localStorage.setItem('refresh_token', response.data.refresh_token);
        
        if (originalRequest.headers) {
          originalRequest.headers['Authorization'] = `Bearer ${response.data.access_token}`;
        }
        
        return api(originalRequest);
      } catch (refreshError) {
        // If refresh fails, logout
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('current_tenant_id');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

// Auth API
export const authApi = {
  login: async (email: string, password: string) => {
    const response = await api.post<LoginResponse>('/api/v1/auth/token', { email, password });
    return response.data;
  },

  verifyTwoFA: async (code: string, tempToken: string) => {
    const response = await axios.post<TokenResponse>(
      `${API_URL}/api/v1/auth/2fa/verify`,
      { code },
      { headers: { Authorization: `Bearer ${tempToken}` } }
    );
    return response.data;
  },

  getCurrentUser: async () => {
    const response = await api.get<User>('/api/v1/auth/me');
    return response.data;
  },
  
  setupTwoFA: async () => {
    const response = await api.post<TwoFASetupResponse>('/auth/2fa/setup');
    return response.data;
  },
  
  enableTwoFA: async (code: string) => {
    const response = await api.post('/auth/2fa/enable', { code });
    return response.data;
  },
  
  disableTwoFA: async (password: string, code: string) => {
    const response = await api.post('/auth/2fa/disable', { password, code });
    return response.data;
  }
};

// Invoices API
export const invoicesApi = {
  getInvoices: async (params?: { status?: string, skip?: number, limit?: number }) => {
    const response = await api.get<Invoice[]>('/invoices', { params });
    return response.data;
  },
  
  getInvoice: async (id: string) => {
    const response = await api.get<Invoice>(`/invoices/${id}`);
    return response.data;
  },
  
  uploadInvoice: async (file: File, supplierName?: string) => {
    const formData = new FormData();
    formData.append('file', file);
    if (supplierName) {
      formData.append('supplier_name', supplierName);
    }
    
    const response = await api.post('/invoices/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
  
  validateInvoice: async (id: string) => {
    const response = await api.put(`/invoices/${id}/validate`);
    return response.data;
  },
  
  deleteInvoice: async (id: string) => {
    const response = await api.delete(`/invoices/${id}`);
    return response.data;
  }
};

// Action Items API
export const actionItemsApi = {
  getActionItems: async (params?: { completed?: boolean, priority?: string, category?: string }) => {
    const response = await api.get<ActionItem[]>('/action-items', { params });
    return response.data;
  },
  
  getActionItem: async (id: string) => {
    const response = await api.get<ActionItem>(`/action-items/${id}`);
    return response.data;
  },
  
  completeActionItem: async (id: string, resolutionNotes?: string) => {
    const response = await api.put(`/action-items/${id}/complete`, { resolution_notes: resolutionNotes });
    return response.data;
  },
  
  reopenActionItem: async (id: string) => {
    const response = await api.put(`/action-items/${id}/reopen`);
    return response.data;
  },
  
  deleteActionItem: async (id: string) => {
    const response = await api.delete(`/action-items/${id}`);
    return response.data;
  }
};

// Users API
export const usersApi = {
  getUsers: async () => {
    const response = await api.get('/users');
    return response.data;
  },
  
  getUser: async (id: string) => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },
  
  updateUserRole: async (userId: string, roleId: string) => {
    const response = await api.put(`/users/${userId}/role`, { role_id: roleId });
    return response.data;
  },
  
  deactivateUser: async (userId: string) => {
    const response = await api.put(`/users/${userId}/deactivate`);
    return response.data;
  },
  
  activateUser: async (userId: string) => {
    const response = await api.put(`/users/${userId}/activate`);
    return response.data;
  },
  
  getRoles: async () => {
    const response = await api.get('/users/roles');
    return response.data;
  }
};
