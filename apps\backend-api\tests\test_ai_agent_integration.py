import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy.orm import Session
from datetime import datetime
import uuid

from app.services.ai_agent_orchestrator import AIAgentOrchestrator
from app.services.ai_agent_action_items import AIAgentActionItemService
from app.models.agent_session import (
    AgentSession, ExecutionPlan, SessionStatus, StepStatus
)
from app.models.action_item import ActionItem
from app.models.tenant import Tenant


class TestAIAgentIntegration:
    """Integration tests for AI Agent system"""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def mock_tenant(self):
        """Mock tenant"""
        tenant = Mock(spec=Tenant)
        tenant.id = uuid.uuid4()
        return tenant
    
    @pytest.fixture
    def execution_plan(self):
        """Create test execution plan"""
        plan = ExecutionPlan(
            id=uuid.uuid4(),
            name="test_invoice_processing",
            description="Test plan for invoice processing",
            version="1.0",
            is_active=True,
            steps=[
                {"step": "get_invoice", "description": "Fetch invoice"},
                {"step": "extract_content", "description": "Extract content"},
                {"step": "web_search", "description": "Enrich context"},
                {"step": "rag_search", "description": "Find accounting codes"},
                {"step": "book_invoice", "description": "Book the invoice"}
            ],
            global_config={"confidence_threshold": 0.8}
        )
        return plan
    
    @pytest.mark.asyncio
    async def test_end_to_end_successful_processing(self, mock_db, mock_tenant, execution_plan):
        """Test complete successful invoice processing workflow"""
        # Setup orchestrator
        orchestrator = AIAgentOrchestrator(mock_db)
        
        # Mock database operations
        mock_db.query.return_value.filter.return_value.first.return_value = execution_plan
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.refresh = Mock()
        
        # Mock duplicate check (no duplicates)
        orchestrator.duplicate_service.check_duplicate_invoice = Mock(return_value=None)
        
        # Mock all tools to succeed
        mock_tools = {}
        for step in execution_plan.steps:
            tool_name = step["step"]
            mock_tool = Mock()
            mock_tool.execute = AsyncMock(return_value={
                "success": True,
                "data": f"output_from_{tool_name}",
                "confidence": 0.9
            })
            mock_tools[tool_name] = mock_tool
        
        def get_mock_tool(tool_name):
            return Mock(return_value=mock_tools[tool_name])
        
        orchestrator.tool_registry.get_tool = get_mock_tool
        
        # Create session
        session = await orchestrator.create_session(
            tenant_id=str(mock_tenant.id),
            execution_plan_name="test_invoice_processing",
            invoice_unique_id="TEST_INVOICE_001",
            source_type="manual",
            source_metadata={"file_path": "/test/invoice.pdf"}
        )
        
        # Verify session created
        assert session.status == SessionStatus.PENDING
        assert session.invoice_unique_id == "TEST_INVOICE_001"
        
        # Mock session retrieval for execution
        mock_db.query.return_value.filter.return_value.first.return_value = session
        
        # Execute session
        result_session = await orchestrator.execute_session(str(session.id))
        
        # Verify successful completion
        assert result_session.status == SessionStatus.COMPLETED
        assert result_session.current_step_index == len(execution_plan.steps)
        
        # Verify all tools were called
        for tool_name, mock_tool in mock_tools.items():
            mock_tool.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_end_to_end_failure_with_action_item(self, mock_db, mock_tenant, execution_plan):
        """Test workflow failure and action item creation"""
        # Setup orchestrator
        orchestrator = AIAgentOrchestrator(mock_db)
        
        # Mock database operations
        mock_db.query.return_value.filter.return_value.first.return_value = execution_plan
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.refresh = Mock()
        
        # Mock duplicate check (no duplicates)
        orchestrator.duplicate_service.check_duplicate_invoice = Mock(return_value=None)
        
        # Mock tools - first one succeeds, second one fails
        mock_get_invoice = Mock()
        mock_get_invoice.execute = AsyncMock(return_value={
            "success": True,
            "invoice_data": {"id": "123", "file_path": "/test/invoice.pdf"}
        })
        
        mock_extract_content = Mock()
        mock_extract_content.execute = AsyncMock(side_effect=Exception("OCR service unavailable"))
        
        def get_mock_tool(tool_name):
            if tool_name == "get_invoice":
                return Mock(return_value=mock_get_invoice)
            elif tool_name == "extract_content":
                return Mock(return_value=mock_extract_content)
            else:
                return Mock(return_value=Mock())
        
        orchestrator.tool_registry.get_tool = get_mock_tool
        
        # Create session
        session = await orchestrator.create_session(
            tenant_id=str(mock_tenant.id),
            execution_plan_name="test_invoice_processing",
            invoice_unique_id="FAILING_INVOICE_001",
            source_type="manual"
        )
        
        # Mock session retrieval for execution
        mock_db.query.return_value.filter.return_value.first.return_value = session
        
        # Execute session (should fail)
        result_session = await orchestrator.execute_session(str(session.id))
        
        # Verify failure
        assert result_session.status == SessionStatus.FAILED
        assert result_session.requires_human_review == True
        assert "OCR service unavailable" in result_session.action_item_reason
        
        # Test action item creation
        action_item_service = AIAgentActionItemService(mock_db)
        
        # Mock user lookup for action item assignment
        from app.models.user import User, TenantUser
        mock_user = Mock(spec=User)
        mock_user.id = uuid.uuid4()
        mock_tenant_user = Mock(spec=TenantUser)
        mock_tenant_user.user_id = mock_user.id
        
        mock_db.query.return_value.join.return_value.filter.return_value.first.return_value = mock_tenant_user
        
        # Create action item
        action_item = action_item_service.create_action_item_for_failed_session(result_session)
        
        # Verify action item
        assert action_item.agent_session_id == result_session.id
        assert action_item.priority == "high"  # Error should be high priority
        assert action_item.category == "error"
        assert "OCR service unavailable" in action_item.description
    
    @pytest.mark.asyncio
    async def test_duplicate_detection_workflow(self, mock_db, mock_tenant, execution_plan):
        """Test duplicate detection preventing processing"""
        # Setup orchestrator
        orchestrator = AIAgentOrchestrator(mock_db)
        
        # Mock existing session (duplicate)
        existing_session = Mock(spec=AgentSession)
        existing_session.id = uuid.uuid4()
        existing_session.status = SessionStatus.COMPLETED
        existing_session.source_type = "erp"
        
        # Mock duplicate detection to find existing session
        orchestrator.duplicate_service.check_duplicate_invoice = Mock(return_value=existing_session)
        
        # Attempt to create session should fail
        from app.services.ai_agent_orchestrator import DuplicateInvoiceError
        
        with pytest.raises(DuplicateInvoiceError) as exc_info:
            await orchestrator.create_session(
                tenant_id=str(mock_tenant.id),
                execution_plan_name="test_invoice_processing",
                invoice_unique_id="DUPLICATE_INVOICE_001",
                source_type="erp"
            )
        
        # Verify error message contains relevant information
        error_message = str(exc_info.value)
        assert "duplicate" in error_message.lower()
        assert str(existing_session.id) in error_message
    
    @pytest.mark.asyncio
    async def test_retry_mechanism(self, mock_db, mock_tenant, execution_plan):
        """Test session retry mechanism"""
        # Setup orchestrator
        orchestrator = AIAgentOrchestrator(mock_db)
        
        # Mock database operations
        mock_db.query.return_value.filter.return_value.first.return_value = execution_plan
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.refresh = Mock()
        
        # Mock duplicate check (no duplicates)
        orchestrator.duplicate_service.check_duplicate_invoice = Mock(return_value=None)
        
        # Mock tool that fails on first try, succeeds on retry
        call_count = 0
        
        async def mock_tool_execute(input_data):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise Exception("Temporary network error")
            else:
                return {"success": True, "data": "retry_success"}
        
        mock_tool = Mock()
        mock_tool.execute = mock_tool_execute
        
        orchestrator.tool_registry.get_tool = Mock(return_value=Mock(return_value=mock_tool))
        
        # Create session
        session = await orchestrator.create_session(
            tenant_id=str(mock_tenant.id),
            execution_plan_name="test_invoice_processing",
            invoice_unique_id="RETRY_INVOICE_001",
            source_type="manual"
        )
        
        # Set max retries to 1
        session.max_retries = 1
        
        # Mock session retrieval for execution
        mock_db.query.return_value.filter.return_value.first.return_value = session
        
        # Execute session (should retry and succeed)
        result_session = await orchestrator.execute_session(str(session.id))
        
        # Verify retry occurred
        assert result_session.retry_count == 1
        # Note: In a real scenario, the session would be completed after retry
        # This test verifies the retry logic is triggered
    
    def test_action_item_resolution(self, mock_db):
        """Test action item resolution workflow"""
        # Setup action item service
        action_item_service = AIAgentActionItemService(mock_db)
        
        # Mock action item
        action_item = Mock(spec=ActionItem)
        action_item.id = uuid.uuid4()
        action_item.tenant_id = uuid.uuid4()
        action_item.agent_session_id = uuid.uuid4()
        action_item.is_completed = False
        
        mock_db.query.return_value.filter.return_value.first.return_value = action_item
        
        # Mock retry task
        with patch('app.tasks.ai_agent_tasks.retry_failed_session_task') as mock_retry_task:
            mock_retry_task.delay = Mock()
            
            # Resolve action item with retry
            user_id = str(uuid.uuid4())
            resolution_notes = "Fixed the OCR service issue and retrying"
            
            resolved_item = action_item_service.resolve_action_item(
                action_item_id=str(action_item.id),
                user_id=user_id,
                resolution_notes=resolution_notes,
                retry_session=True
            )
            
            # Verify resolution
            assert action_item.is_completed == True
            assert action_item.completed_by == user_id
            assert resolution_notes in action_item.resolution_notes
            
            # Verify retry was triggered
            mock_retry_task.delay.assert_called_once_with(str(action_item.agent_session_id))


if __name__ == "__main__":
    pytest.main([__file__])
