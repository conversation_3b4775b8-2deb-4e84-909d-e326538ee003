from celery import current_task
from sqlalchemy.orm import Session
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional

from app.celery_app import celery_app
from app.database import SessionLocal, set_tenant_context
from app.services.ai_agent_orchestrator import AIAgentOrchestrator, DuplicateInvoiceError
from app.models.agent_session import AgentSession, SessionStatus, StepStatus
from app.tasks.notifications import send_action_item_notification_task

logger = logging.getLogger(__name__)


@celery_app.task(bind=True)
def ai_agent_process_invoice_task(
    self, 
    tenant_id: str,
    execution_plan_name: str = "invoice_processing_v1",
    invoice_unique_id: Optional[str] = None,
    source_type: str = "manual",
    source_metadata: Optional[Dict[str, Any]] = None
):
    """
    AI Agent task for processing invoices with full orchestration

    This is the main task for all invoice processing in the system
    """
    db = SessionLocal()
    session_id = None
    
    try:
        # Set tenant context
        set_tenant_context(db, tenant_id)
        
        # Update task progress
        self.update_state(
            state='PROGRESS', 
            meta={
                'step': 'initializing_ai_agent', 
                'progress': 10,
                'tenant_id': tenant_id,
                'execution_plan': execution_plan_name
            }
        )
        
        # Create AI Agent Orchestrator
        orchestrator = AIAgentOrchestrator(db)
        
        # Create session
        self.update_state(
            state='PROGRESS', 
            meta={
                'step': 'creating_session', 
                'progress': 20,
                'invoice_id': invoice_unique_id
            }
        )
        
        session = asyncio.run(orchestrator.create_session(
            tenant_id=tenant_id,
            execution_plan_name=execution_plan_name,
            invoice_unique_id=invoice_unique_id,
            source_type=source_type,
            source_metadata=source_metadata or {}
        ))
        
        session_id = str(session.id)
        logger.info(f"Created AI agent session {session_id} for tenant {tenant_id}")
        
        # Execute session
        self.update_state(
            state='PROGRESS', 
            meta={
                'step': 'executing_session', 
                'progress': 30,
                'session_id': session_id
            }
        )
        
        # Run the async execution in the current event loop
        session = asyncio.run(orchestrator.execute_session(session_id))
        
        # Update final progress based on session status
        if session.status == SessionStatus.COMPLETED:
            self.update_state(
                state='SUCCESS', 
                meta={
                    'step': 'completed', 
                    'progress': 100,
                    'session_id': session_id,
                    'status': 'completed'
                }
            )
            
            logger.info(f"AI agent session {session_id} completed successfully")
            return {
                "success": True,
                "session_id": session_id,
                "status": "completed",
                "tenant_id": tenant_id
            }
            
        elif session.status == SessionStatus.FAILED:
            # Handle failed session - create action item
            if session.requires_human_review:
                # Send notification for human review (will create action item)
                send_action_item_notification_task.delay(
                    tenant_id=tenant_id,
                    session_id=session_id,
                    reason=session.action_item_reason
                )
            
            self.update_state(
                state='FAILURE', 
                meta={
                    'step': 'failed', 
                    'progress': 100,
                    'session_id': session_id,
                    'status': 'failed',
                    'reason': session.action_item_reason
                }
            )
            
            logger.error(f"AI agent session {session_id} failed: {session.action_item_reason}")
            return {
                "success": False,
                "session_id": session_id,
                "status": "failed",
                "reason": session.action_item_reason,
                "requires_review": session.requires_human_review
            }
        
        else:
            # Unexpected status
            logger.warning(f"AI agent session {session_id} ended with unexpected status: {session.status}")
            return {
                "success": False,
                "session_id": session_id,
                "status": str(session.status),
                "reason": "Unexpected session status"
            }
            
    except DuplicateInvoiceError as e:
        # Handle duplicate invoice error
        logger.warning(f"Duplicate invoice detected: {e}")
        
        if session_id:
            # Send notification about duplicate (will create action item)
            send_action_item_notification_task.delay(
                tenant_id=tenant_id,
                session_id=session_id,
                reason=f"Duplicate invoice: {str(e)}"
            )
        
        self.update_state(
            state='FAILURE', 
            meta={
                'step': 'duplicate_detected', 
                'progress': 100,
                'error': str(e),
                'error_type': 'duplicate'
            }
        )
        
        return {
            "success": False,
            "error": str(e),
            "error_type": "duplicate",
            "session_id": session_id
        }
        
    except Exception as e:
        # Handle unexpected errors
        logger.error(f"Unexpected error in AI agent task: {e}", exc_info=True)
        
        if session_id:
            # Try to mark session as failed
            try:
                session = db.query(AgentSession).filter(AgentSession.id == session_id).first()
                if session:
                    session.status = SessionStatus.FAILED
                    session.requires_human_review = True
                    session.action_item_reason = f"Task error: {str(e)}"
                    session.completed_at = datetime.utcnow()
                    db.commit()
                    
                    # Send notification (will create action item)
                    send_action_item_notification_task.delay(
                        tenant_id=tenant_id,
                        session_id=session_id,
                        reason=f"Task error: {str(e)}"
                    )
            except Exception as cleanup_error:
                logger.error(f"Error during cleanup: {cleanup_error}")
        
        self.update_state(
            state='FAILURE', 
            meta={
                'step': 'error', 
                'progress': 100,
                'error': str(e),
                'error_type': 'unexpected'
            }
        )
        
        return {
            "success": False,
            "error": str(e),
            "error_type": "unexpected",
            "session_id": session_id
        }
        
    finally:
        db.close()


@celery_app.task(bind=True)
def ai_agent_fetch_invoices_task(self):
    """
    Scheduled task to fetch invoices and process them with AI agent

    This is the main task for ERP integration and scheduled processing
    """
    db = SessionLocal()
    try:
        from app.models.tenant import Tenant
        from app.services.integration_service import IntegrationService
        from datetime import timedelta
        
        # Get all active tenants
        tenants = db.query(Tenant).filter(Tenant.is_active == True).all()
        
        total_processed = 0
        integration_service = IntegrationService(db)
        
        for tenant in tenants:
            try:
                # Set tenant context
                set_tenant_context(db, str(tenant.id))
                
                # Get last fetch time (or default to 24 hours ago)
                last_fetch = datetime.now(timezone.utc) - timedelta(hours=24)
                
                logger.info(f"Fetching invoices for tenant {tenant.id} since {last_fetch}")
                
                # Fetch invoices from all active integrations for this tenant
                sync_result = asyncio.run(integration_service.sync_all_tenant_integrations(
                    str(tenant.id),
                    last_fetch
                ))
                
                invoices_data = sync_result.get("invoices", [])
                
                # Process each invoice with AI agent
                for invoice_data in invoices_data:
                    try:
                        # Extract unique ID from invoice data
                        invoice_unique_id = invoice_data.get("id") or invoice_data.get("invoice_id")
                        
                        if not invoice_unique_id:
                            logger.warning(f"Invoice without unique ID found for tenant {tenant.id}, skipping")
                            continue
                        
                        # Create AI agent task for this invoice
                        ai_agent_process_invoice_task.delay(
                            tenant_id=str(tenant.id),
                            execution_plan_name="invoice_processing_v1",
                            invoice_unique_id=str(invoice_unique_id),
                            source_type="erp",
                            source_metadata={
                                "integration_type": invoice_data.get("integration_type"),
                                "original_data": invoice_data
                            }
                        )
                        
                        total_processed += 1
                        
                    except Exception as invoice_error:
                        logger.error(f"Error processing invoice {invoice_data.get('id')} for tenant {tenant.id}: {invoice_error}")
                        continue
                
                logger.info(f"Queued {len(invoices_data)} invoices for AI agent processing for tenant {tenant.id}")
                
            except Exception as tenant_error:
                logger.error(f"Error processing tenant {tenant.id}: {tenant_error}")
                continue
        
        logger.info(f"AI agent fetch task completed. Total invoices queued: {total_processed}")
        
        return {
            "success": True,
            "total_processed": total_processed,
            "tenants_processed": len(tenants)
        }
        
    except Exception as e:
        logger.error(f"Error in AI agent fetch task: {e}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }
        
    finally:
        db.close()


@celery_app.task(bind=True)
def retry_failed_session_task(self, session_id: str):
    """
    Manual task to retry a failed AI agent session
    """
    db = SessionLocal()
    try:
        # Get session
        session = db.query(AgentSession).filter(AgentSession.id == session_id).first()
        if not session:
            logger.error(f"Session {session_id} not found")
            return {"error": "Session not found"}
        
        # Set tenant context
        set_tenant_context(db, str(session.tenant_id))
        
        # Check if session can be retried
        if session.status not in [SessionStatus.FAILED]:
            logger.error(f"Session {session_id} cannot be retried (status: {session.status})")
            return {"error": f"Session cannot be retried (status: {session.status})"}
        
        # Reset session for retry
        session.status = SessionStatus.PENDING
        session.current_step_index = 0
        session.retry_count = 0
        session.requires_human_review = False
        session.action_item_reason = None
        session.started_at = None
        session.completed_at = None
        
        # Reset all steps
        for step in session.execution_steps:
            step.status = StepStatus.PENDING
            step.started_at = None
            step.completed_at = None
            step.error_message = None
            step.input_data = None
            step.output_data = None
        
        db.commit()
        
        # Create AI Agent Orchestrator and execute
        orchestrator = AIAgentOrchestrator(db)
        session = asyncio.run(orchestrator.execute_session(session_id))
        
        logger.info(f"Retried session {session_id} with status: {session.status}")
        
        return {
            "success": True,
            "session_id": session_id,
            "status": str(session.status),
            "requires_review": session.requires_human_review
        }
        
    except Exception as e:
        logger.error(f"Error retrying session {session_id}: {e}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }
        
    finally:
        db.close()


@celery_app.task(bind=True)
def cleanup_temp_files_task(self):
    """
    Clean up old temporary files
    Maintenance task for system cleanup
    """
    import os
    import tempfile
    from datetime import datetime, timedelta, timezone

    try:
        temp_dir = tempfile.gettempdir()
        cutoff_time = datetime.now() - timedelta(days=7)  # Remove files older than 7 days

        cleaned_count = 0
        total_size = 0

        # Look for invoice-related temp files
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                if file.startswith(('invoice_', 'upload_', 'temp_invoice')):
                    file_path = os.path.join(root, file)
                    try:
                        file_stat = os.stat(file_path)
                        file_time = datetime.fromtimestamp(file_stat.st_mtime)

                        if file_time < cutoff_time:
                            file_size = file_stat.st_size
                            os.remove(file_path)
                            cleaned_count += 1
                            total_size += file_size
                            logger.info(f"Cleaned up temp file: {file_path}")

                    except Exception as file_error:
                        logger.warning(f"Could not clean up file {file_path}: {file_error}")

        logger.info(f"Cleanup completed: {cleaned_count} files removed, {total_size} bytes freed")

        return {
            "success": True,
            "files_cleaned": cleaned_count,
            "bytes_freed": total_size
        }

    except Exception as e:
        logger.error(f"Error in cleanup task: {e}")
        return {
            "success": False,
            "error": str(e)
        }
