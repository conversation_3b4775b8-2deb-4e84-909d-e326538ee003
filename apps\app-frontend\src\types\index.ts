export interface User {
  id: string;
  email: string;
  is_2fa_enabled: boolean;
  tenants: TenantInfo[];
}

export interface TenantInfo {
  id: string;
  name: string;
  role: string;
  permissions: string[];
}

export interface LoginResponse {
  access_token?: string;
  temp_token?: string;
  token_type: string;
  requires_2fa: boolean;
  message: string;
}

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

export interface Invoice {
  id: string;
  supplier_name: string;
  invoice_number?: string;
  invoice_date?: string;
  due_date?: string;
  total_amount?: number;
  currency?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'needs_review';
  created_at: string;
  updated_at: string;
  original_filename: string;
  extracted_text?: string;
  extracted_context?: string;
  processing_error?: string;
  accounting_entries?: AccountingEntry[];
}

export interface AccountingEntry {
  id: string;
  account_code: string;
  account_name: string;
  debit_amount?: number;
  credit_amount?: number;
  description?: string;
  confidence_score: number;
  is_validated: boolean;
}

export interface ActionItem {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'review' | 'validation' | 'error' | 'manual_entry';
  is_completed: boolean;
  created_at: string;
  invoice_id?: string;
  assigned_user?: {
    id: string;
    email: string;
  };
  invoice?: {
    id: string;
    supplier_name: string;
    status: string;
  };
}

export interface TwoFASetupResponse {
  secret: string;
  qr_code: string;
  backup_codes: string[];
}

export interface ApiError {
  detail: string;
}

// Integration types
export type IntegrationType = 'FORTNOX' | 'VISMA' | 'HTTP';

export interface IntegrationConfigField {
  name: string;
  type: 'string' | 'url' | 'select';
  required: boolean;
  description: string;
  options?: string[];
  sensitive?: boolean;
}

export interface AvailableIntegration {
  type: IntegrationType;
  name: string;
  description: string;
  oauth_required: boolean;
  config_fields: IntegrationConfigField[];
}

export interface InvoiceIntegration {
  id: string;
  tenant_id: string;
  integration_type: IntegrationType;
  name?: string;
  description?: string;
  is_active: boolean;
  last_sync_at?: string;
  last_sync_status?: 'success' | 'failed' | 'partial';
  last_error?: string;
  created_at: string;
  updated_at: string;
}

export interface InvoiceIntegrationCreate {
  integration_type: IntegrationType;
  configuration: Record<string, any>;
  name?: string;
  description?: string;
  is_active?: boolean;
}

export interface InvoiceIntegrationUpdate {
  configuration?: Record<string, any>;
  name?: string;
  description?: string;
  is_active?: boolean;
}

export interface IntegrationSyncResult {
  integration_id: string;
  integration_type: IntegrationType;
  success: boolean;
  invoices_count: number;
  message: string;
  error_details?: Record<string, any>;
  synced_at?: string;
}

export interface ScheduleSettings {
  enabled: boolean;
  cron_expression: string;
  last_run?: string;
  next_run?: string;
}

export interface ScheduleSettingsUpdate {
  enabled?: boolean;
  cron_expression?: string;
}

export interface ConnectionTestResult {
  success: boolean;
  message: string;
  details?: Record<string, any>;
  tested_at?: string;
}

// Utility function to combine CSS class names
export function classNames(...classes: string[]): string {
  return classes.filter(Boolean).join(' ');
}
