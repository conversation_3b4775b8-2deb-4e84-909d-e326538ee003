from pydantic import BaseModel, field_validator
from typing import Optional, List
from uuid import UUID
from app.utils.email_validator import pydantic_email_validator


class LoginRequest(BaseModel):
    email: str
    password: str

    @field_validator('email')
    @classmethod
    def validate_email(cls, v: str) -> str:
        """Validate email using our custom validator."""
        return pydantic_email_validator(v)


class LoginResponse(BaseModel):
    access_token: Optional[str] = None
    temp_token: Optional[str] = None
    token_type: str = "bearer"
    requires_2fa: bool = False
    message: str


class TwoFAVerifyRequest(BaseModel):
    code: str


class TwoFASetupResponse(BaseModel):
    secret: str
    qr_code: str
    backup_codes: List[str]


class TwoFAEnableRequest(BaseModel):
    code: str


class TwoFADisableRequest(BaseModel):
    password: str
    code: str


class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"


class UserInfo(BaseModel):
    id: UUID
    email: str
    is_2fa_enabled: bool
    tenants: List['TenantInfo']

    class Config:
        from_attributes = True


class TenantInfo(BaseModel):
    id: UUID
    name: str
    role: str
    permissions: List[str]

    class Config:
        from_attributes = True


class RefreshTokenRequest(BaseModel):
    refresh_token: str


# Update forward references
UserInfo.model_rebuild()
