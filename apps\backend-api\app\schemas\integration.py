"""
Pydantic schemas for invoice integrations.
"""

from pydantic import BaseModel, <PERSON>, validator
from typing import Dict, Any, Optional, List
from datetime import datetime
from uuid import UUID
from enum import Enum


class IntegrationType(str, Enum):
    """Supported integration types"""
    FORTNOX = "FORTNOX"
    VISMA = "VISMA"
    HTTP = "HTTP"


class IntegrationStatus(str, Enum):
    """Integration sync status"""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"


class OAuth2TokenSchema(BaseModel):
    """Schema for OAuth2 token data"""
    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = "Bearer"
    expires_at: Optional[datetime] = None
    scope: Optional[str] = None
    
    class Config:
        from_attributes = True


class OAuth2TokenResponse(BaseModel):
    """Response schema for OAuth2 token"""
    id: UUID
    provider: str
    token_type: str
    expires_at: Optional[datetime] = None
    scope: Optional[str] = None
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True


class IntegrationConfigBase(BaseModel):
    """Base configuration for integrations"""
    pass


class FortnoxConfig(IntegrationConfigBase):
    """Configuration for Fortnox integration"""
    client_id: str
    client_secret: str
    access_token: Optional[str] = None
    authorization_code: Optional[str] = None


class VismaConfig(IntegrationConfigBase):
    """Configuration for Visma eEkonomi integration"""
    client_id: str
    client_secret: str
    access_token: Optional[str] = None
    authorization_code: Optional[str] = None


class HttpConfig(IntegrationConfigBase):
    """Configuration for simple HTTP integration"""
    base_url: str
    auth_type: str = Field(default="none", pattern="^(none|basic|bearer|api_key)$")
    username: Optional[str] = None
    password: Optional[str] = None
    api_key: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    
    @validator('base_url')
    def validate_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError('URL must start with http:// or https://')
        return v


class InvoiceIntegrationCreate(BaseModel):
    """Schema for creating invoice integration"""
    integration_type: IntegrationType
    configuration: Dict[str, Any]
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: bool = True


class InvoiceIntegrationUpdate(BaseModel):
    """Schema for updating invoice integration"""
    configuration: Optional[Dict[str, Any]] = None
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


class InvoiceIntegrationResponse(BaseModel):
    """Response schema for invoice integration"""
    id: UUID
    tenant_id: UUID
    integration_type: str
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: bool
    last_sync_at: Optional[datetime] = None
    last_sync_status: Optional[str] = None
    last_error: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class AvailableIntegration(BaseModel):
    """Schema for available integration types"""
    type: IntegrationType
    name: str
    description: str
    oauth_required: bool
    config_fields: List[Dict[str, Any]]


class ConnectionTestResult(BaseModel):
    """Schema for connection test results"""
    success: bool
    message: str
    details: Optional[Dict[str, Any]] = None
    tested_at: datetime


class OAuth2LoginRequest(BaseModel):
    """Schema for OAuth2 login request"""
    integration_id: UUID
    redirect_uri: Optional[str] = None


class OAuth2LoginResponse(BaseModel):
    """Schema for OAuth2 login response"""
    authorization_url: str
    state: str


class OAuth2CallbackRequest(BaseModel):
    """Schema for OAuth2 callback"""
    code: str
    state: str
    integration_id: UUID


class InvoiceData(BaseModel):
    """Schema for standardized invoice data from integrations"""
    external_id: str
    invoice_number: str
    supplier_name: str
    invoice_date: Optional[str] = None
    due_date: Optional[str] = None
    total_amount: Optional[float] = None
    currency: str = "SEK"
    file_url: str
    file_type: str = "pdf"
    metadata: Optional[Dict[str, Any]] = None


class IntegrationSyncResult(BaseModel):
    """Schema for integration sync results"""
    integration_id: UUID
    integration_type: str
    success: bool
    invoices_count: int
    message: str
    error_details: Optional[Dict[str, Any]] = None
    synced_at: Optional[datetime] = None


class ScheduleSettings(BaseModel):
    """Settings for scheduled sync"""
    enabled: bool
    cron_expression: str = Field(default="0 */2 * * *", description="Cron expression for schedule")
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None


class ScheduleSettingsUpdate(BaseModel):
    """Update schedule settings"""
    enabled: Optional[bool] = None
    cron_expression: Optional[str] = None
