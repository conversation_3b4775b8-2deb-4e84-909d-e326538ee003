from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import logging
import hashlib
import json

from app.models.agent_session import AgentSession, SessionStatus
from app.database import set_tenant_context

logger = logging.getLogger(__name__)


class DuplicateDetectionService:
    """Service for detecting duplicate invoices across different scenarios"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def check_duplicate_invoice(
        self, 
        tenant_id: str,
        invoice_unique_id: Optional[str] = None,
        source_type: str = "manual",
        source_metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[AgentSession]:
        """
        Comprehensive duplicate detection based on different criteria
        
        Args:
            tenant_id: Tenant ID for isolation
            invoice_unique_id: Unique ID from source system (if available)
            source_type: Type of source (erp, http, manual)
            source_metadata: Additional metadata for duplicate detection
            
        Returns:
            Existing session if duplicate found, None otherwise
        """
        set_tenant_context(self.db, tenant_id)
        
        # Strategy 1: Exact ID match (for ERP sources)
        if invoice_unique_id and source_type != "http":
            exact_match = self._check_exact_id_match(tenant_id, invoice_unique_id)
            if exact_match:
                logger.info(f"Exact ID duplicate found: {invoice_unique_id} in session {exact_match.id}")
                return exact_match
        
        # Strategy 2: Content-based detection (for HTTP and manual sources)
        if source_type in ["http", "manual"] and source_metadata:
            content_match = self._check_content_based_duplicate(tenant_id, source_metadata)
            if content_match:
                logger.info(f"Content-based duplicate found in session {content_match.id}")
                return content_match
        
        # Strategy 3: Metadata-based detection (for all sources)
        if source_metadata:
            metadata_match = self._check_metadata_based_duplicate(tenant_id, source_metadata, source_type)
            if metadata_match:
                logger.info(f"Metadata-based duplicate found in session {metadata_match.id}")
                return metadata_match
        
        return None
    
    def _check_exact_id_match(self, tenant_id: str, invoice_unique_id: str) -> Optional[AgentSession]:
        """Check for exact invoice ID match"""
        return self.db.query(AgentSession).filter(
            AgentSession.tenant_id == tenant_id,
            AgentSession.invoice_unique_id == invoice_unique_id,
            AgentSession.status.in_([SessionStatus.COMPLETED, SessionStatus.RUNNING])
        ).first()
    
    def _check_content_based_duplicate(
        self, 
        tenant_id: str, 
        source_metadata: Dict[str, Any]
    ) -> Optional[AgentSession]:
        """
        Check for duplicates based on content hash
        
        This is useful for HTTP sources where the same file might be uploaded multiple times
        """
        # Extract content identifiers
        content_hash = source_metadata.get("content_hash")
        file_size = source_metadata.get("file_size")
        filename = source_metadata.get("filename")
        
        if not any([content_hash, file_size, filename]):
            return None
        
        # Look for sessions with similar content metadata
        query = self.db.query(AgentSession).filter(
            AgentSession.tenant_id == tenant_id,
            AgentSession.status.in_([SessionStatus.COMPLETED, SessionStatus.RUNNING])
        )
        
        # Check recent sessions (last 30 days) to avoid false positives
        recent_cutoff = datetime.utcnow() - timedelta(days=30)
        query = query.filter(AgentSession.created_at >= recent_cutoff)
        
        sessions = query.all()
        
        for session in sessions:
            session_metadata = session.source_metadata or {}
            
            # Check content hash match (strongest indicator)
            if content_hash and session_metadata.get("content_hash") == content_hash:
                return session
            
            # Check file size + filename combination
            if (file_size and filename and 
                session_metadata.get("file_size") == file_size and 
                session_metadata.get("filename") == filename):
                return session
        
        return None
    
    def _check_metadata_based_duplicate(
        self, 
        tenant_id: str, 
        source_metadata: Dict[str, Any],
        source_type: str
    ) -> Optional[AgentSession]:
        """
        Check for duplicates based on business metadata
        
        This looks for invoices with same supplier, amount, date, etc.
        """
        # Extract business identifiers
        supplier_name = source_metadata.get("supplier_name")
        invoice_number = source_metadata.get("invoice_number")
        total_amount = source_metadata.get("total_amount")
        invoice_date = source_metadata.get("invoice_date")
        
        # Need at least supplier + (invoice_number OR amount+date) for meaningful comparison
        if not supplier_name:
            return None
        
        if not (invoice_number or (total_amount and invoice_date)):
            return None
        
        # Look for sessions with similar business metadata
        query = self.db.query(AgentSession).filter(
            AgentSession.tenant_id == tenant_id,
            AgentSession.status.in_([SessionStatus.COMPLETED, SessionStatus.RUNNING])
        )
        
        # Check recent sessions (last 90 days for business duplicates)
        recent_cutoff = datetime.utcnow() - timedelta(days=90)
        query = query.filter(AgentSession.created_at >= recent_cutoff)
        
        sessions = query.all()
        
        for session in sessions:
            session_metadata = session.source_metadata or {}
            
            # Check supplier name match (case-insensitive)
            session_supplier = session_metadata.get("supplier_name", "").lower()
            if supplier_name.lower() != session_supplier:
                continue
            
            # Check invoice number match
            if invoice_number and session_metadata.get("invoice_number") == invoice_number:
                return session
            
            # Check amount + date combination
            if (total_amount and invoice_date and
                session_metadata.get("total_amount") == total_amount and
                session_metadata.get("invoice_date") == invoice_date):
                return session
        
        return None
    
    def create_content_hash(self, file_path: str) -> str:
        """Create a hash of file content for duplicate detection"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"Error creating content hash for {file_path}: {e}")
            return ""
    
    def extract_business_metadata(self, extracted_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract business metadata from extracted invoice context
        
        This is used to populate source_metadata for duplicate detection
        """
        metadata = {}
        
        # Extract supplier information
        if "supplier_name" in extracted_context:
            metadata["supplier_name"] = extracted_context["supplier_name"]
        elif "vendor_name" in extracted_context:
            metadata["supplier_name"] = extracted_context["vendor_name"]
        
        # Extract invoice identifiers
        if "invoice_number" in extracted_context:
            metadata["invoice_number"] = extracted_context["invoice_number"]
        
        if "invoice_id" in extracted_context:
            metadata["invoice_id"] = extracted_context["invoice_id"]
        
        # Extract amounts
        if "total_amount" in extracted_context:
            metadata["total_amount"] = extracted_context["total_amount"]
        elif "amount" in extracted_context:
            metadata["total_amount"] = extracted_context["amount"]
        
        # Extract dates
        if "invoice_date" in extracted_context:
            metadata["invoice_date"] = extracted_context["invoice_date"]
        elif "date" in extracted_context:
            metadata["invoice_date"] = extracted_context["date"]
        
        # Extract due date
        if "due_date" in extracted_context:
            metadata["due_date"] = extracted_context["due_date"]
        
        return metadata
    
    def get_duplicate_statistics(self, tenant_id: str, days: int = 30) -> Dict[str, Any]:
        """Get statistics about duplicate detection for a tenant"""
        set_tenant_context(self.db, tenant_id)
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Get all sessions in the period
        all_sessions = self.db.query(AgentSession).filter(
            AgentSession.tenant_id == tenant_id,
            AgentSession.created_at >= cutoff_date
        ).all()
        
        # Count duplicates (sessions that failed due to duplicate detection)
        duplicate_sessions = [
            s for s in all_sessions 
            if s.status == SessionStatus.FAILED and 
            s.action_item_reason and 
            "duplicate" in s.action_item_reason.lower()
        ]
        
        # Group by source type
        source_stats = {}
        for session in all_sessions:
            source_type = session.source_type
            if source_type not in source_stats:
                source_stats[source_type] = {"total": 0, "duplicates": 0}
            
            source_stats[source_type]["total"] += 1
            
            if session in duplicate_sessions:
                source_stats[source_type]["duplicates"] += 1
        
        return {
            "period_days": days,
            "total_sessions": len(all_sessions),
            "duplicate_sessions": len(duplicate_sessions),
            "duplicate_rate": len(duplicate_sessions) / len(all_sessions) if all_sessions else 0,
            "source_statistics": source_stats,
            "recent_duplicates": [
                {
                    "session_id": str(s.id),
                    "invoice_id": s.invoice_unique_id,
                    "source_type": s.source_type,
                    "reason": s.action_item_reason,
                    "created_at": s.created_at.isoformat()
                }
                for s in duplicate_sessions[-10:]  # Last 10 duplicates
            ]
        }
