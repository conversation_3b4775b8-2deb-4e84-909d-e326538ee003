import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { 
  PlusIcon, 
  TrashIcon, 
  PencilIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  CloudArrowUpIcon
} from '@heroicons/react/24/outline';
import { integrationsApi } from '../services/api';
import { 
  AvailableIntegration, 
  InvoiceIntegration, 
  InvoiceIntegrationCreate,
  InvoiceIntegrationUpdate,
  IntegrationType 
} from '../types';
import toast from 'react-hot-toast';

interface IntegrationSettingsProps {
  className?: string;
}

export default function IntegrationSettings({ className = '' }: IntegrationSettingsProps) {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingIntegration, setEditingIntegration] = useState<InvoiceIntegration | null>(null);
  const queryClient = useQueryClient();

  const { data: availableIntegrations, isLoading: loadingAvailable } = useQuery(
    'available-integrations',
    integrationsApi.getAvailableIntegrations
  );

  const { data: integrations, isLoading: loadingIntegrations } = useQuery(
    'integrations',
    integrationsApi.getIntegrations
  );

  const createMutation = useMutation(integrationsApi.createIntegration, {
    onSuccess: () => {
      queryClient.invalidateQueries('integrations');
      setShowCreateForm(false);
      toast.success('Integration created successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Failed to create integration');
    },
  });

  const updateMutation = useMutation(
    ({ id, data }: { id: string; data: InvoiceIntegrationUpdate }) =>
      integrationsApi.updateIntegration(id, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('integrations');
        setEditingIntegration(null);
        toast.success('Integration updated successfully');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to update integration');
      },
    }
  );

  const deleteMutation = useMutation(integrationsApi.deleteIntegration, {
    onSuccess: () => {
      queryClient.invalidateQueries('integrations');
      toast.success('Integration deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Failed to delete integration');
    },
  });

  const testConnectionMutation = useMutation(integrationsApi.testConnection, {
    onSuccess: (result) => {
      if (result.success) {
        toast.success('Connection test successful');
      } else {
        toast.error(`Connection test failed: ${result.message}`);
      }
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Connection test failed');
    },
  });

  const handleCreateIntegration = (data: InvoiceIntegrationCreate) => {
    createMutation.mutate(data);
  };

  const handleUpdateIntegration = (id: string, data: InvoiceIntegrationUpdate) => {
    updateMutation.mutate({ id, data });
  };

  const handleDeleteIntegration = (id: string) => {
    if (window.confirm('Are you sure you want to delete this integration?')) {
      deleteMutation.mutate(id);
    }
  };

  const handleTestConnection = (id: string) => {
    testConnectionMutation.mutate(id);
  };

  const getStatusIcon = (integration: InvoiceIntegration) => {
    if (!integration.is_active) {
      return <XCircleIcon className="h-5 w-5 text-gray-400" />;
    }
    
    switch (integration.last_sync_status) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'partial':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <CloudArrowUpIcon className="h-5 w-5 text-blue-500" />;
    }
  };

  const getStatusText = (integration: InvoiceIntegration) => {
    if (!integration.is_active) return 'Inactive';
    
    switch (integration.last_sync_status) {
      case 'success':
        return 'Connected';
      case 'failed':
        return 'Error';
      case 'partial':
        return 'Warning';
      default:
        return 'Not synced';
    }
  };

  if (loadingAvailable || loadingIntegrations) {
    return (
      <div className="card-container">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="card-container">
        <div className="flex items-center justify-between mb-6">
          <h3 className="section-title flex items-center">
            <CloudArrowUpIcon className="h-6 w-6 mr-2 text-indigo-600" />
            Invoice Integrations
          </h3>
          <button
            onClick={() => setShowCreateForm(true)}
            className="btn-small flex items-center"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Integration
          </button>
        </div>

        {/* Integration List */}
        <div className="space-y-4">
          {integrations?.length === 0 ? (
            <div className="text-center py-8">
              <CloudArrowUpIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No integrations configured</p>
              <button
                onClick={() => setShowCreateForm(true)}
                className="btn-primary"
              >
                Add Your First Integration
              </button>
            </div>
          ) : (
            integrations?.map((integration) => (
              <div
                key={integration.id}
                className="bg-gradient-to-r from-purple-50 via-indigo-50 to-pink-50 rounded-2xl p-6"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {getStatusIcon(integration)}
                    <div>
                      <h4 className="font-semibold text-gray-900">
                        {integration.name || integration.integration_type}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {integration.description || `${integration.integration_type} Integration`}
                      </p>
                      <div className="flex items-center space-x-4 mt-2">
                        <span className="text-xs text-gray-500">
                          Status: {getStatusText(integration)}
                        </span>
                        {integration.last_sync_at && (
                          <span className="text-xs text-gray-500">
                            Last sync: {new Date(integration.last_sync_at).toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleTestConnection(integration.id)}
                      disabled={testConnectionMutation.isLoading}
                      className="btn-small bg-blue-600 hover:bg-blue-700"
                    >
                      Test
                    </button>
                    <button
                      onClick={() => setEditingIntegration(integration)}
                      className="btn-small bg-gray-600 hover:bg-gray-700"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteIntegration(integration.id)}
                      disabled={deleteMutation.isLoading}
                      className="btn-small bg-red-600 hover:bg-red-700"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                {integration.last_error && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm text-red-700">{integration.last_error}</p>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>

      {/* Create Integration Modal */}
      {showCreateForm && (
        <IntegrationForm
          availableIntegrations={availableIntegrations || []}
          onSubmit={handleCreateIntegration}
          onCancel={() => setShowCreateForm(false)}
          isLoading={createMutation.isLoading}
        />
      )}

      {/* Edit Integration Modal */}
      {editingIntegration && (
        <IntegrationForm
          availableIntegrations={availableIntegrations || []}
          integration={editingIntegration}
          onSubmit={(data) => handleUpdateIntegration(editingIntegration.id, data)}
          onCancel={() => setEditingIntegration(null)}
          isLoading={updateMutation.isLoading}
        />
      )}
    </div>
  );
}

// Integration Form Component
interface IntegrationFormProps {
  availableIntegrations: AvailableIntegration[];
  integration?: InvoiceIntegration;
  onSubmit: (data: any) => void;
  onCancel: () => void;
  isLoading: boolean;
}

function IntegrationForm({ 
  availableIntegrations, 
  integration, 
  onSubmit, 
  onCancel, 
  isLoading 
}: IntegrationFormProps) {
  const [selectedType, setSelectedType] = useState<IntegrationType>(
    integration?.integration_type || 'FORTNOX'
  );
  const [formData, setFormData] = useState({
    name: integration?.name || '',
    description: integration?.description || '',
    is_active: integration?.is_active ?? true,
    configuration: {} as Record<string, any>
  });

  const selectedIntegration = availableIntegrations.find(i => i.type === selectedType);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const data = integration 
      ? {
          name: formData.name,
          description: formData.description,
          is_active: formData.is_active,
          configuration: formData.configuration
        }
      : {
          integration_type: selectedType,
          name: formData.name,
          description: formData.description,
          is_active: formData.is_active,
          configuration: formData.configuration
        };

    onSubmit(data);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-semibold mb-4">
          {integration ? 'Edit Integration' : 'Add Integration'}
        </h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {!integration && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Integration Type
              </label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value as IntegrationType)}
                className="select-custom"
              >
                {availableIntegrations.map((int) => (
                  <option key={int.type} value={int.type}>
                    {int.name}
                  </option>
                ))}
              </select>
              {selectedIntegration && (
                <p className="text-sm text-gray-600 mt-1">
                  {selectedIntegration.description}
                </p>
              )}
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="input-custom"
              placeholder="My Integration"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="input-custom"
              rows={3}
              placeholder="Description of this integration"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active"
              checked={formData.is_active}
              onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
              Active
            </label>
          </div>

          {/* Configuration Fields */}
          {selectedIntegration?.config_fields.map((field) => (
            <div key={field.name}>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {field.description}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </label>
              {field.type === 'select' ? (
                <select
                  value={formData.configuration[field.name] || ''}
                  onChange={(e) => setFormData({
                    ...formData,
                    configuration: {
                      ...formData.configuration,
                      [field.name]: e.target.value
                    }
                  })}
                  className="select-custom"
                  required={field.required}
                >
                  <option value="">Select...</option>
                  {field.options?.map((option) => (
                    <option key={option} value={option}>
                      {option}
                    </option>
                  ))}
                </select>
              ) : (
                <input
                  type={field.sensitive ? 'password' : field.type === 'url' ? 'url' : 'text'}
                  value={formData.configuration[field.name] || ''}
                  onChange={(e) => setFormData({
                    ...formData,
                    configuration: {
                      ...formData.configuration,
                      [field.name]: e.target.value
                    }
                  })}
                  className="input-custom"
                  required={field.required}
                  placeholder={field.type === 'url' ? 'https://example.com' : ''}
                />
              )}
            </div>
          ))}

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary"
            >
              {isLoading ? 'Saving...' : integration ? 'Update' : 'Create'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
