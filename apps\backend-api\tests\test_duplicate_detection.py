import pytest
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import uuid
import tempfile
import os

from app.services.duplicate_detection import DuplicateDetectionService
from app.models.agent_session import AgentSession, SessionStatus


class TestDuplicateDetectionService:
    """Test cases for Duplicate Detection Service"""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def service(self, mock_db):
        """Create service instance"""
        return DuplicateDetectionService(mock_db)
    
    @pytest.fixture
    def mock_session(self):
        """Mock agent session"""
        session = Mock(spec=AgentSession)
        session.id = uuid.uuid4()
        session.tenant_id = uuid.uuid4()
        session.status = SessionStatus.COMPLETED
        session.created_at = datetime.utcnow()
        return session
    
    def test_check_exact_id_match_found(self, service, mock_db, mock_session):
        """Test exact ID match detection"""
        tenant_id = str(uuid.uuid4())
        invoice_id = "INVOICE_123"
        
        # Setup mock to return existing session
        mock_db.query.return_value.filter.return_value.first.return_value = mock_session
        
        result = service._check_exact_id_match(tenant_id, invoice_id)
        
        assert result == mock_session
        mock_db.query.assert_called()
    
    def test_check_exact_id_match_not_found(self, service, mock_db):
        """Test exact ID match when no duplicate exists"""
        tenant_id = str(uuid.uuid4())
        invoice_id = "UNIQUE_INVOICE_123"
        
        # Setup mock to return None
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        result = service._check_exact_id_match(tenant_id, invoice_id)
        
        assert result is None
    
    def test_check_content_based_duplicate_hash_match(self, service, mock_db, mock_session):
        """Test content-based duplicate detection with hash match"""
        tenant_id = str(uuid.uuid4())
        content_hash = "abc123def456"
        
        source_metadata = {
            "content_hash": content_hash,
            "file_size": 1024,
            "filename": "test.pdf"
        }
        
        # Setup existing session with same hash
        mock_session.source_metadata = {"content_hash": content_hash}
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_session]
        
        result = service._check_content_based_duplicate(tenant_id, source_metadata)
        
        assert result == mock_session
    
    def test_check_content_based_duplicate_file_match(self, service, mock_db, mock_session):
        """Test content-based duplicate detection with file size + name match"""
        tenant_id = str(uuid.uuid4())
        
        source_metadata = {
            "file_size": 2048,
            "filename": "invoice.pdf"
        }
        
        # Setup existing session with same file size and name
        mock_session.source_metadata = {
            "file_size": 2048,
            "filename": "invoice.pdf"
        }
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_session]
        
        result = service._check_content_based_duplicate(tenant_id, source_metadata)
        
        assert result == mock_session
    
    def test_check_content_based_duplicate_no_match(self, service, mock_db, mock_session):
        """Test content-based duplicate detection with no match"""
        tenant_id = str(uuid.uuid4())
        
        source_metadata = {
            "content_hash": "unique_hash",
            "file_size": 1024,
            "filename": "unique.pdf"
        }
        
        # Setup existing session with different metadata
        mock_session.source_metadata = {
            "content_hash": "different_hash",
            "file_size": 2048,
            "filename": "different.pdf"
        }
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_session]
        
        result = service._check_content_based_duplicate(tenant_id, source_metadata)
        
        assert result is None
    
    def test_check_metadata_based_duplicate_invoice_number_match(self, service, mock_db, mock_session):
        """Test metadata-based duplicate detection with invoice number match"""
        tenant_id = str(uuid.uuid4())
        
        source_metadata = {
            "supplier_name": "ACME Corp",
            "invoice_number": "INV-2024-001",
            "total_amount": 1000.00
        }
        
        # Setup existing session with same supplier and invoice number
        mock_session.source_metadata = {
            "supplier_name": "ACME Corp",
            "invoice_number": "INV-2024-001",
            "total_amount": 1500.00  # Different amount, but invoice number matches
        }
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_session]
        
        result = service._check_metadata_based_duplicate(tenant_id, source_metadata, "erp")
        
        assert result == mock_session
    
    def test_check_metadata_based_duplicate_amount_date_match(self, service, mock_db, mock_session):
        """Test metadata-based duplicate detection with amount + date match"""
        tenant_id = str(uuid.uuid4())
        
        source_metadata = {
            "supplier_name": "ACME Corp",
            "total_amount": 1000.00,
            "invoice_date": "2024-01-15"
        }
        
        # Setup existing session with same supplier, amount, and date
        mock_session.source_metadata = {
            "supplier_name": "acme corp",  # Case insensitive match
            "total_amount": 1000.00,
            "invoice_date": "2024-01-15"
        }
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_session]
        
        result = service._check_metadata_based_duplicate(tenant_id, source_metadata, "manual")
        
        assert result == mock_session
    
    def test_check_metadata_based_duplicate_no_supplier(self, service, mock_db):
        """Test metadata-based duplicate detection without supplier name"""
        tenant_id = str(uuid.uuid4())
        
        source_metadata = {
            "invoice_number": "INV-2024-001",
            "total_amount": 1000.00
        }
        
        result = service._check_metadata_based_duplicate(tenant_id, source_metadata, "erp")
        
        # Should return None because supplier name is required
        assert result is None
    
    def test_check_duplicate_invoice_erp_exact_match(self, service, mock_db, mock_session):
        """Test comprehensive duplicate check for ERP source with exact ID match"""
        tenant_id = str(uuid.uuid4())
        invoice_id = "ERP_INVOICE_123"
        
        # Mock exact ID match
        service._check_exact_id_match = Mock(return_value=mock_session)
        
        result = service.check_duplicate_invoice(
            tenant_id=str(tenant_id),
            invoice_unique_id=invoice_id,
            source_type="erp"
        )
        
        assert result == mock_session
        service._check_exact_id_match.assert_called_once_with(str(tenant_id), invoice_id)
    
    def test_check_duplicate_invoice_http_content_match(self, service, mock_db, mock_session):
        """Test comprehensive duplicate check for HTTP source with content match"""
        tenant_id = str(uuid.uuid4())
        
        source_metadata = {
            "content_hash": "abc123",
            "file_size": 1024
        }
        
        # Mock content-based match
        service._check_content_based_duplicate = Mock(return_value=mock_session)
        service._check_metadata_based_duplicate = Mock(return_value=None)
        
        result = service.check_duplicate_invoice(
            tenant_id=str(tenant_id),
            source_type="http",
            source_metadata=source_metadata
        )
        
        assert result == mock_session
        service._check_content_based_duplicate.assert_called_once()
    
    def test_check_duplicate_invoice_no_match(self, service, mock_db):
        """Test comprehensive duplicate check with no matches"""
        tenant_id = str(uuid.uuid4())
        
        # Mock all checks returning None
        service._check_exact_id_match = Mock(return_value=None)
        service._check_content_based_duplicate = Mock(return_value=None)
        service._check_metadata_based_duplicate = Mock(return_value=None)
        
        result = service.check_duplicate_invoice(
            tenant_id=str(tenant_id),
            invoice_unique_id="unique_id",
            source_type="erp",
            source_metadata={"test": "data"}
        )
        
        assert result is None
    
    def test_create_content_hash(self, service):
        """Test content hash creation"""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("Test content for hashing")
            temp_path = f.name
        
        try:
            # Create hash
            hash1 = service.create_content_hash(temp_path)
            hash2 = service.create_content_hash(temp_path)
            
            # Verify hash is consistent
            assert hash1 == hash2
            assert len(hash1) == 32  # MD5 hash length
            assert hash1.isalnum()  # Should be alphanumeric
            
        finally:
            # Clean up
            os.unlink(temp_path)
    
    def test_create_content_hash_file_not_found(self, service):
        """Test content hash creation with non-existent file"""
        hash_result = service.create_content_hash("/non/existent/file.txt")
        
        # Should return empty string on error
        assert hash_result == ""
    
    def test_extract_business_metadata(self, service):
        """Test business metadata extraction"""
        extracted_context = {
            "supplier_name": "ACME Corporation",
            "invoice_number": "INV-2024-001",
            "total_amount": 1500.50,
            "invoice_date": "2024-01-15",
            "due_date": "2024-02-15",
            "line_items": [
                {"description": "Product A", "amount": 1000.00},
                {"description": "Product B", "amount": 500.50}
            ]
        }
        
        metadata = service.extract_business_metadata(extracted_context)
        
        assert metadata["supplier_name"] == "ACME Corporation"
        assert metadata["invoice_number"] == "INV-2024-001"
        assert metadata["total_amount"] == 1500.50
        assert metadata["invoice_date"] == "2024-01-15"
        assert metadata["due_date"] == "2024-02-15"
    
    def test_extract_business_metadata_alternative_fields(self, service):
        """Test business metadata extraction with alternative field names"""
        extracted_context = {
            "vendor_name": "Vendor Corp",  # Alternative to supplier_name
            "amount": 750.25,  # Alternative to total_amount
            "date": "2024-01-20"  # Alternative to invoice_date
        }
        
        metadata = service.extract_business_metadata(extracted_context)
        
        assert metadata["supplier_name"] == "Vendor Corp"
        assert metadata["total_amount"] == 750.25
        assert metadata["invoice_date"] == "2024-01-20"
    
    def test_get_duplicate_statistics(self, service, mock_db):
        """Test duplicate statistics generation"""
        tenant_id = str(uuid.uuid4())
        
        # Create mock sessions
        duplicate_session = Mock(spec=AgentSession)
        duplicate_session.status = SessionStatus.FAILED
        duplicate_session.action_item_reason = "Duplicate invoice detected"
        duplicate_session.source_type = "erp"
        duplicate_session.id = uuid.uuid4()
        duplicate_session.invoice_unique_id = "DUP_001"
        duplicate_session.created_at = datetime.utcnow()
        
        normal_session = Mock(spec=AgentSession)
        normal_session.status = SessionStatus.COMPLETED
        normal_session.action_item_reason = None
        normal_session.source_type = "manual"
        
        mock_db.query.return_value.filter.return_value.all.return_value = [
            duplicate_session, normal_session
        ]
        
        stats = service.get_duplicate_statistics(tenant_id, days=30)
        
        assert stats["total_sessions"] == 2
        assert stats["duplicate_sessions"] == 1
        assert stats["duplicate_rate"] == 0.5
        assert "erp" in stats["source_statistics"]
        assert "manual" in stats["source_statistics"]
        assert stats["source_statistics"]["erp"]["duplicates"] == 1
        assert stats["source_statistics"]["manual"]["duplicates"] == 0
        assert len(stats["recent_duplicates"]) == 1


if __name__ == "__main__":
    pytest.main([__file__])
