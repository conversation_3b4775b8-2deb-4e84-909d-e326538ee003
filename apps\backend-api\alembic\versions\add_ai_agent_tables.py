"""Add AI Agent tables

Revision ID: 011
Revises: 010
Create Date: 2025-01-03 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '011'
down_revision = '010'
branch_labels = None
depends_on = None


def upgrade():
    # Create execution_plans table
    op.create_table('execution_plans',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('version', sa.String(length=50), nullable=False),
        sa.Column('is_active', sa.<PERSON>(), nullable=False),
        sa.Column('steps', postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column('global_config', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    
    # Create agent_sessions table
    op.create_table('agent_sessions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('session_name', sa.String(length=255), nullable=False),
        sa.Column('status', sa.Enum('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED', name='sessionstatus'), nullable=False),
        sa.Column('execution_plan_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('invoice_unique_id', sa.String(length=255), nullable=True),
        sa.Column('source_type', sa.String(length=50), nullable=False),
        sa.Column('source_metadata', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('current_step_index', sa.Integer(), nullable=False),
        sa.Column('retry_count', sa.Integer(), nullable=False),
        sa.Column('max_retries', sa.Integer(), nullable=False),
        sa.Column('requires_human_review', sa.Boolean(), nullable=False),
        sa.Column('action_item_reason', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['execution_plan_id'], ['execution_plans.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_agent_sessions_tenant_id'), 'agent_sessions', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_agent_sessions_invoice_unique_id'), 'agent_sessions', ['invoice_unique_id'], unique=False)
    
    # Create execution_steps table
    op.create_table('execution_steps',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('session_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('step_name', sa.String(length=255), nullable=False),
        sa.Column('step_index', sa.Integer(), nullable=False),
        sa.Column('status', sa.Enum('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'SKIPPED', name='stepstatus'), nullable=False),
        sa.Column('step_config', postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column('input_data', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('output_data', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['session_id'], ['agent_sessions.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_execution_steps_session_id'), 'execution_steps', ['session_id'], unique=False)
    op.create_index(op.f('ix_execution_steps_tenant_id'), 'execution_steps', ['tenant_id'], unique=False)
    
    # Create tool_results table
    op.create_table('tool_results',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('session_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('step_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tool_name', sa.String(length=255), nullable=False),
        sa.Column('tool_version', sa.String(length=50), nullable=True),
        sa.Column('input_parameters', postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column('output_data', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('success', sa.Boolean(), nullable=False),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('execution_time_ms', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['session_id'], ['agent_sessions.id'], ),
        sa.ForeignKeyConstraint(['step_id'], ['execution_steps.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tool_results_session_id'), 'tool_results', ['session_id'], unique=False)
    op.create_index(op.f('ix_tool_results_step_id'), 'tool_results', ['step_id'], unique=False)
    op.create_index(op.f('ix_tool_results_tenant_id'), 'tool_results', ['tenant_id'], unique=False)
    
    # Create thought_chains table
    op.create_table('thought_chains',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('session_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('step_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('sequence_number', sa.Integer(), nullable=False),
        sa.Column('thought_type', sa.String(length=100), nullable=False),
        sa.Column('content', postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column('context', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['session_id'], ['agent_sessions.id'], ),
        sa.ForeignKeyConstraint(['step_id'], ['execution_steps.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_thought_chains_session_id'), 'thought_chains', ['session_id'], unique=False)
    op.create_index(op.f('ix_thought_chains_step_id'), 'thought_chains', ['step_id'], unique=False)
    op.create_index(op.f('ix_thought_chains_tenant_id'), 'thought_chains', ['tenant_id'], unique=False)
    
    # Insert default execution plan
    op.execute("""
        INSERT INTO execution_plans (id, name, description, version, is_active, steps, global_config, created_at, updated_at)
        VALUES (
            gen_random_uuid(),
            'invoice_processing_v1',
            'Standard invoice processing workflow with AI agent orchestration',
            '1.0',
            true,
            '[
                {"step": "get_invoice", "description": "Hämta faktura"},
                {"step": "extract_content", "description": "Bestäm fil format och extrahera faktura på lämpligast sätt"},
                {"step": "web_search", "description": "Berika kontext, vi måste ha information om vem leverantören är och vad som köps på fakturan"},
                {"step": "rag_search", "description": "Bestäm bokföringskonto genom att göra en RAG sökning mot vectordatabsen"},
                {"step": "book_invoice", "description": "Bokför fakturan"}
            ]'::json,
            '{"confidence_threshold": 0.8, "max_retries": 1}'::json,
            NOW(),
            NOW()
        )
    """)
    
    # Add RLS policies for tenant isolation
    op.execute("""
        -- Enable RLS on all new tables
        ALTER TABLE agent_sessions ENABLE ROW LEVEL SECURITY;
        ALTER TABLE execution_steps ENABLE ROW LEVEL SECURITY;
        ALTER TABLE tool_results ENABLE ROW LEVEL SECURITY;
        ALTER TABLE thought_chains ENABLE ROW LEVEL SECURITY;
        
        -- Create RLS policies
        CREATE POLICY agent_sessions_tenant_isolation ON agent_sessions
            USING (tenant_id = current_setting('app.current_tenant_id')::uuid);
            
        CREATE POLICY execution_steps_tenant_isolation ON execution_steps
            USING (tenant_id = current_setting('app.current_tenant_id')::uuid);
            
        CREATE POLICY tool_results_tenant_isolation ON tool_results
            USING (tenant_id = current_setting('app.current_tenant_id')::uuid);
            
        CREATE POLICY thought_chains_tenant_isolation ON thought_chains
            USING (tenant_id = current_setting('app.current_tenant_id')::uuid);
    """)


def downgrade():
    # Drop RLS policies
    op.execute("""
        DROP POLICY IF EXISTS agent_sessions_tenant_isolation ON agent_sessions;
        DROP POLICY IF EXISTS execution_steps_tenant_isolation ON execution_steps;
        DROP POLICY IF EXISTS tool_results_tenant_isolation ON tool_results;
        DROP POLICY IF EXISTS thought_chains_tenant_isolation ON thought_chains;
    """)
    
    # Drop tables in reverse order
    op.drop_table('thought_chains')
    op.drop_table('tool_results')
    op.drop_table('execution_steps')
    op.drop_table('agent_sessions')
    op.drop_table('execution_plans')
    
    # Drop enums
    op.execute("DROP TYPE IF EXISTS sessionstatus")
    op.execute("DROP TYPE IF EXISTS stepstatus")
